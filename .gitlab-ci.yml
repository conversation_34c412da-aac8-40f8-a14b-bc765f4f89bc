stages:
    - build

docker-build-master:
    # Official docker image.
    image: docker:latest
    stage: build
    services:
        - docker:dind
    script:
        - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
        - docker build -t $CI_REGISTRY/julianhandl/zemagpt:$CI_PIPELINE_ID -t $CI_REGISTRY/julianhandl/zemagpt:latest .
        - docker push $CI_REGISTRY/julianhandl/zemagpt:$CI_PIPELINE_ID
        - docker push $CI_REGISTRY/julianhandl/zemagpt:latest
        # - docker build --pull -t $CI_REGISTRY/"$CI_REGISTRY_IMAGE:$CI_PIPELINE_ID" .
        # - docker push "$CI_REGISTRY/$CI_REGISTRY_IMAGE:$CI_PIPELINE_ID"
    only:
        refs:
            - main