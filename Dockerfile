# Use an official Python runtime as a parent image
FROM python:3.11 as builder

# Set the working directory in the container
WORKDIR /usr/src/app

COPY requirements.txt requirements.txt
# Install any needed packages specified in requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copy only the necessary files into the container at /app

COPY main.py .
COPY static static
COPY templates templates
COPY chat chat
COPY search search
COPY data data

# Run the database setup script
RUN python3 data/tools/create_db.py

# Stage 2: Production
FROM python:3.11-alpine

# Set the working directory
WORKDIR /app

# Copy only the necessary files from the build stage
COPY --from=builder /usr/src/app /app

# Make port 5000 available to the world outside this container
EXPOSE 5000

# Define environment variable
ENV FLASK_APP=main.py

# Run app.py when the container launches
CMD ["flask", "run", "--host", "0.0.0.0"]