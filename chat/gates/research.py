from pydantic import BaseModel, <PERSON>
from typing import Literal

from langchain_openai import ChatOpenAI
from openai import OpenAI
from pydantic import BaseModel
from chat.state import State

class ResearchDecission(BaseModel):
    """Decide if we need to do more research or if we can answer the question."""

    binary_score: str = Field(
        description="Research score: 'yes' if additional research is needed, or 'no' if research is completed"
    )


model = ChatOpenAI(model="gpt-4.1-mini", temperature=0.1)

def reasarch_gate(state: State) -> Literal["research", "final_answer"]:
    print(state)

    prompt = """
    You are an AI assistant that helps users with their questions.
    You need to decide if you need to do more research or if you can answer the question based on the context.
    Be aware that you answers must be facts based on the context. Do not answer if you are not sure about the answer.
    Give a binary score 'yes' or 'no' score to indicate whether further research is needed to answer the question.

    The context:
    {context}

    The question:
    {prompt}
    """.format(context=state['messages'], prompt=state['prompt'])

    response = model.with_structured_output(ResearchDecission).invoke(
        [{"role": "user", "content": prompt}]
    )

    score = response.binary_score

    if score == "yes":
        return "research"
    else:
        return "final_answer"