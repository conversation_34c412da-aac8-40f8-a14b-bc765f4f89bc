import os
from typing import Annotated
from typing_extensions import TypedDict
from langchain_openai.chat_models import ChatOpenAI
from langgraph.graph.state import StateGraph, START
from langchain_core.messages import AIMessage, HumanMessage
from langgraph.graph.message import add_messages
from langgraph.prebuilt import tools_condition
from chat.tools.organisation_search import getOrganisationInformation
from chat.tools.person_search import getPersonInformation
from chat.tools.semantic_search import semanticSearchTool

llm_simple = ChatOpenAI(api_key=os.environ.get("OPENAI_API_KEY"), model_name='gpt-4.1-mini', temperature=0.4)
llm_advanced = ChatOpenAI(api_key=os.environ.get("OPENAI_API_KEY"), model_name='gpt-4.1', temperature=0.4)

llm_with_all_tools = llm_simple.bind_tools(tools=[
    getOrganisationInformation,
    getPersonInformation,
    semanticSearchTool
], tool_choice='auto')

llm_with_semantic_search = llm_advanced.bind_tools(tools=[
    getPersonInformation,
], tool_choice='auto')

llm_with_person_search = llm_simple.bind_tools(tools=[
    getPersonInformation,
], tool_choice='auto')

# STATE

class State(TypedDict):
    prompt: str
    messages: Annotated[list, add_messages]

subgraph_builder = StateGraph(State)

# Subgraph

def research(state: State):
    prompt = """
    Du bist ein AI-Assistent der eine semantische Suche bedient.
    Erstelle einen optimierten Text zur semantischen Suche, basierend auf einer Frage.
    
    Die Frage:
    {query}
    """.format(query=state['prompt'])
    semantic_input = llm_simple.invoke(prompt)

    response = semanticSearchTool(semantic_input.content)

    summary_prompt = """
    Erstelle eine Zusammenfassung des Inhalt im Bezug auf eine Frage!
    
    Die Frage:
    {question}

    Der Inhalt:
    {content}
    """.format(question=state['prompt'], content=response)
    summary = llm_simple.invoke(summary_prompt)

    return {"messages": [AIMessage(content=summary.content)]}

def tool_resolver(state: State):
    response = llm_with_all_tools.invoke(state["messages"])
    return {"messages": response}

def answer(state: State):
    # return an ai message as summary
    return {"messages": [AIMessage(content="Rosa Stattler ist eine österreichische Kommunalpolitikerin und wohnt in Arb.")]}

subgraph_builder.add_node("research", research)

subgraph_builder.add_node("tool_resolver", tool_resolver)
subgraph_builder.add_node("answer", answer)

subgraph_builder.add_edge(START, "research")
subgraph_builder.add_conditional_edges("research",tools_condition)
subgraph_builder.add_edge("tool_resolver", "answer")
subgraph_builder.add_edge("tools", "tool_resolver")


# semantic research edgminie
# person research edge
# organisation research edge

subgraph_builder.add_edge("research", "answer")

research_graph = subgraph_builder.compile()