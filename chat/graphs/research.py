import os
from typing import Annotated, Literal, Optional
from typing_extensions import TypedDict
from langchain_openai.chat_models import ChatOpenAI
from langgraph.graph.state import StateGraph, START, END
from langchain_core.messages import AIMessage
from langgraph.graph.message import add_messages
from chat.tools.organisation_search import getOrganisationInformation
from chat.tools.person_search import getPersonInformation
from chat.tools.semantic_search import semanticSearchTool
from pydantic import BaseModel, Field

llm_simple = ChatOpenAI(api_key=os.environ.get("OPENAI_API_KEY"), model_name='gpt-4.1-mini', temperature=0.4)
llm_advanced = ChatOpenAI(api_key=os.environ.get("OPENAI_API_KEY"), model_name='gpt-4.1', temperature=0.4)

# STATE

class ResearchDecision(BaseModel):
    """Decision model for research sufficiency evaluation."""
    is_sufficient: bool = Field(description="True if research is sufficient to answer the question, False if more research is needed")
    reasoning: str = Field(description="Brief explanation of the decision")

class SearchDecision(BaseModel):
    """Decision model for determining which type of search to perform next."""
    search_type: Literal["semantic", "person", "organization", "none"] = Field(
        description="Type of search to perform: 'semantic' for topic search, 'person' for people search, 'organization' for organization search, 'none' if no search needed"
    )
    search_query: Optional[str] = Field(description="Query to use for the search, if applicable")
    person_names: Optional[list[str]] = Field(description="List of person names to search for, if search_type is 'person'")
    organization_name: Optional[str] = Field(description="Organization name to search for, if search_type is 'organization'")

class State(TypedDict):
    prompt: str
    messages: Annotated[list, add_messages]
    research_iterations: int
    collected_information: list[str]
    max_iterations: int

# Initialize subgraph
subgraph_builder = StateGraph(State)

# RESEARCH NODES

def initialize_research(state: State):
    """Initialize the research process with default values. State parameter required by LangGraph."""
    return {
        "research_iterations": 0,
        "collected_information": [],
        "max_iterations": 3,
        "messages": [AIMessage(content="Starte Forschungsprozess...")]
    }

def research_coordinator(state: State):
    """Decide what type of search to perform next based on the prompt and current context."""

    context_info = "\n".join(state.get("collected_information", []))

    prompt = f"""
    Du bist ein Forschungskoordinator. Analysiere die Benutzerfrage und entscheide, welche Art von Suche als nächstes durchgeführt werden soll.

    Benutzerfrage: {state['prompt']}

    Bereits gesammelte Informationen:
    {context_info if context_info else "Keine Informationen bisher gesammelt."}

    Entscheide, welche Art von Suche durchgeführt werden soll:
    - "semantic": Für allgemeine Themensuche über Zelking-Matzleinsdorf
    - "person": Wenn spezifische Personen erwähnt werden oder gesucht werden
    - "organization": Wenn nach Organisationen, Vereinen, Institutionen gefragt wird
    - "none": Wenn keine weitere Suche nötig ist

    Wenn du eine Suche auswählst, gib auch die entsprechenden Parameter an.
    """

    response = llm_simple.with_structured_output(SearchDecision).invoke([
        {"role": "user", "content": prompt}
    ])

    return {
        "messages": [AIMessage(content=f"Nächste Suche: {response.search_type} - {response.reasoning if hasattr(response, 'reasoning') else ''}")],
        "search_decision": response
    }

def semantic_search_node(state: State):
    """Perform semantic search based on the prompt."""
    search_decision = state.get("search_decision")
    query = search_decision.search_query if search_decision and search_decision.search_query else state['prompt']

    # Optimize query for semantic search
    optimization_prompt = f"""
    Erstelle einen optimierten Suchtext für eine semantische Suche über Zelking-Matzleinsdorf.

    Ursprüngliche Frage: {query}

    Erstelle einen präzisen deutschen Suchtext:
    """

    optimized_query = llm_simple.invoke(optimization_prompt)
    response = semanticSearchTool(optimized_query.content)

    # Add to collected information
    new_info = f"Semantische Suche für '{query}':\n{response}"
    collected_info = state.get("collected_information", [])
    collected_info.append(new_info)

    return {
        "messages": [AIMessage(content=f"Semantische Suche durchgeführt für: {query}")],
        "collected_information": collected_info
    }

def person_search_node(state: State):
    """Perform person search based on identified names."""
    search_decision = state.get("search_decision")

    if not search_decision or not search_decision.person_names:
        return {"messages": [AIMessage(content="Keine Personen für die Suche identifiziert.")]}

    response = getPersonInformation(search_decision.person_names)

    # Add to collected information
    new_info = f"Personensuche für {', '.join(search_decision.person_names)}:\n{response}"
    collected_info = state.get("collected_information", [])
    collected_info.append(new_info)

    return {
        "messages": [AIMessage(content=f"Personensuche durchgeführt für: {', '.join(search_decision.person_names)}")],
        "collected_information": collected_info
    }

def organization_search_node(state: State):
    """Perform organization search based on identified organization."""
    search_decision = state.get("search_decision")

    if not search_decision or not search_decision.organization_name:
        return {"messages": [AIMessage(content="Keine Organisation für die Suche identifiziert.")]}

    response = getOrganisationInformation(search_decision.organization_name)

    # Add to collected information
    new_info = f"Organisationssuche für {search_decision.organization_name}:\n{response}"
    collected_info = state.get("collected_information", [])
    collected_info.append(new_info)

    return {
        "messages": [AIMessage(content=f"Organisationssuche durchgeführt für: {search_decision.organization_name}")],
        "collected_information": collected_info
    }

def evaluate_research(state: State):
    """Evaluate if the collected research is sufficient to answer the question."""

    context_info = "\n\n".join(state.get("collected_information", []))
    iterations = state.get("research_iterations", 0)
    max_iterations = state.get("max_iterations", 3)

    prompt = f"""
    Bewerte, ob die gesammelten Informationen ausreichen, um die Benutzerfrage zu beantworten.

    Benutzerfrage: {state['prompt']}

    Gesammelte Informationen:
    {context_info if context_info else "Keine Informationen gesammelt."}

    Aktuelle Iteration: {iterations + 1} von {max_iterations}

    Entscheide, ob die Informationen ausreichen oder ob weitere Recherche nötig ist.
    Berücksichtige dabei, dass nach {max_iterations} Iterationen die Recherche beendet werden sollte.
    """

    response = llm_advanced.with_structured_output(ResearchDecision).invoke([
        {"role": "user", "content": prompt}
    ])

    # Increment iteration counter
    new_iterations = iterations + 1

    return {
        "messages": [AIMessage(content=f"Bewertung: {'Ausreichend' if response.is_sufficient else 'Weitere Recherche nötig'} - {response.reasoning}")],
        "research_iterations": new_iterations,
        "research_sufficient": response.is_sufficient or new_iterations >= max_iterations
    }

def final_answer(state: State):
    """Generate the final answer based on all collected research."""

    context_info = "\n\n".join(state.get("collected_information", []))

    prompt = f"""
    Erstelle eine umfassende Antwort auf die Benutzerfrage basierend auf den gesammelten Informationen.

    Benutzerfrage: {state['prompt']}

    Gesammelte Forschungsinformationen:
    {context_info if context_info else "Keine spezifischen Informationen gefunden."}

    Erstelle eine präzise, faktische Antwort auf Deutsch. Wenn keine ausreichenden Informationen
    verfügbar sind, gib das ehrlich zu und erkläre, was gefunden wurde.
    """

    response = llm_advanced.invoke(prompt)

    return {"messages": [AIMessage(content=response.content)]}

# ROUTING FUNCTIONS

def route_search_type(state: State) -> Literal["semantic_search", "person_search", "organization_search", "evaluate_research"]:
    """Route to the appropriate search node based on the coordinator's decision."""
    search_decision = state.get("search_decision")

    if not search_decision:
        return "evaluate_research"

    if search_decision.search_type == "semantic":
        return "semantic_search"
    elif search_decision.search_type == "person":
        return "person_search"
    elif search_decision.search_type == "organization":
        return "organization_search"
    else:
        return "evaluate_research"

def route_research_continuation(state: State) -> Literal["research_coordinator", "final_answer"]:
    """Route based on whether research is sufficient or should continue."""
    is_sufficient = state.get("research_sufficient", False)

    if is_sufficient:
        return "final_answer"
    else:
        return "research_coordinator"

# BUILD THE GRAPH

# Add all nodes
subgraph_builder.add_node("initialize_research", initialize_research)
subgraph_builder.add_node("research_coordinator", research_coordinator)
subgraph_builder.add_node("semantic_search", semantic_search_node)
subgraph_builder.add_node("person_search", person_search_node)
subgraph_builder.add_node("organization_search", organization_search_node)
subgraph_builder.add_node("evaluate_research", evaluate_research)
subgraph_builder.add_node("final_answer", final_answer)

# Add edges
subgraph_builder.add_edge(START, "initialize_research")
subgraph_builder.add_edge("initialize_research", "research_coordinator")

# Conditional edges for routing search types
subgraph_builder.add_conditional_edges(
    "research_coordinator",
    route_search_type,
    {
        "semantic_search": "semantic_search",
        "person_search": "person_search",
        "organization_search": "organization_search",
        "evaluate_research": "evaluate_research"
    }
)

# All search nodes go to evaluation
subgraph_builder.add_edge("semantic_search", "evaluate_research")
subgraph_builder.add_edge("person_search", "evaluate_research")
subgraph_builder.add_edge("organization_search", "evaluate_research")

# Conditional edge for continuing or finishing research
subgraph_builder.add_conditional_edges(
    "evaluate_research",
    route_research_continuation,
    {
        "research_coordinator": "research_coordinator",
        "final_answer": "final_answer"
    }
)

# Final answer ends the graph
subgraph_builder.add_edge("final_answer", END)

# Compile the research graph
research_graph = subgraph_builder.compile()