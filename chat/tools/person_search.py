from langchain_core.tools import tool
from data.service import getKnowledge, semanticSearch, getAvailablePeople

getAllPeople = getAvailablePeople()

@tool(description=f"""
    This tool can get short biographies about people.
    You can query the people by providing a list of names in this format "[Firstname] [Lastname]"
    It queries an sql database for the provided name and returns text about each person.
    The response is a string which contains full sentences that describe the person.

    This tool is usefull if you know exactly the name of the person you are looking for.
    Do not use it with more than 10 names.

    The following people are available:
    {', '.join(getAllPeople)}
    """)
def getPersonInformation(namesAsFirstnameLastname: list[str]) -> str:

    knowledge: list[str] = []

    for nameAsFirstnameLastname in namesAsFirstnameLastname:
        person_knowledge = getKnowledge(nameAsFirstnameLastname)
        if person_knowledge is None or len(person_knowledge) < 1:
            knowledge.extend(f"Wir haben leider keine Informationen über {nameAsFirstnameLastname}.")
            continue
        text = '\n'.join(person_knowledge)
        knowledge.append(f"{nameAsFirstnameLastname}:\n{text}")

    return '\n\n'.join(knowledge)
