import os
from typing import Optional, TypedDict
from langchain_core.tools import tool
from mistralai import Mistral
from pydantic import BaseModel
from data.service import semanticSearch
from search import all_search
from pymilvus.model.reranker import BGERerankFunction

from search.Search import ResultItem

# Here are all the search tools available for the chatbot

## 0. Search for everything unspecific
## 1. Search for documents related to the Gemeinderat
## 2. Search for documents related to the Gemeinde
## 3. Search for documents related to the Feuerwehr Männersdorf
## 4. Search for documents related to the Feuerwehr Zelking
## 5. Search for documents related to the Feuerwehr Matzleinsdorf
## 6. Search for specific people

@tool
def semanticSearchTool(query: str,
                    filter: Optional[str],
                    ) -> str:
    """
    This tool can search for information about the topics that are related to "Zelking-Matzleinsdorf".
    Possible informations are:
      - Protocols of the community council
      - Information about the fire department
      - Information about the community website
      - Information about people living and working in Zelking-Matzleinsdorf

    The query must be a full sentence and written in GERMAN.

    The filter argument is a string that will be used in the milvus search filter.
    available metadata fields are:
    - sourceDate: str (the date of the content in iso format)
    
    Example:
    Searching for documents that were created in 2025 or after:
    filter = "sourceDate > '2025'"

    Filters can be combined with logical and/or operators.

    The result can be sorted by date if you provide the sortByDate argument.
    Possible values are:
    - "asc" (ascending)
    - "desc" (descending)

    You can specify the limit of the number of results you want to get.
    The default value is 5.
    """

    print(f"Searching for: {query}")
    print(f"Filter: {filter}")

    result_items: list[ResultItem] = []
    # advance rag with hypothetical questions

    ## get the initial vector search for the user query
    original_result = semanticSearch(query, filter, 5)
    result_items.extend(original_result)

    ## create a unique list of all the questions_results based on the sourceName
    final_unique_documents: list[ResultItem] = []
    for document in result_items:
        if document['sourceName'] not in [d['sourceName'] for d in final_unique_documents]:
            final_unique_documents.append(document)
    
    ## reranking
    document_result_texts = result_items_to_string(final_unique_documents)

    # reranked_results = bge_rf(
    #     query=query,
    #     documents=document_result_texts,
    #     top_k=2,
    # )

    # print("Reranked results:")
    # print(reranked_results)

    final_text = "\n\n\n\n".join(document_result_texts)
    return final_text

def result_items_to_string(ResultDocuments: list[ResultItem]) -> list[str]:
    documents = []
    for doc in ResultDocuments:
        documents.append(
            """
            ====
            Titel: {sourceTitle}
            Datum: {sourceDate}
            Dateiname: {sourceName}
            ====
            {originalText}
            """.format(
                sourceName=doc['sourceName'],
                sourceTitle=doc['title'],
                sourceDate=doc['sourceDate'],
                originalText=doc['text']
            )
        )
    return documents
