# Crawler
The crawler of this repo crawls different sources of data and makes them available for the RAG system.

## Data strategy
The overall data strategy is to crawl data from different sources and make it available for the RAG system.
The majority of data is just raw, crawled data with metadata that is store in a vector db,
and can be semantically searched with filters on top.
For a better RAG result for the user and also to support deep researches,
we also store curated data in a sqlite database. That way the RAG system can also
perform detailed queries on information like events, news, people and much more.

### The crawling process
The whole crawling process is split into two parts:
1. crawling the raw data
2. crawling the curated data
3. Generate additional metadata from both
    - Knowledge about people

At first we always crawl the raw data.
Then we will crawl a currated list of source for events and other specific informations.

After the raw and currated crawling process are done,
we will extract additional metadata from those source.
This metadata contains knowledge about people.

All the crawled data is stored inside the data folder in the root of the repo.
During the crawling process the new crawled data replaces the old crawled data.
All the data is stored in json files.

During startup of the application, the whole data from the json files
is loaded into the vector db and the sqlite database.

## Parts of the crawler
It is split into two parts: the raw data crawler and the curated data crawler.

### Raw data (raw)
The raw data crawler just crawls data like websites,
converts them to markdown and creates metadata for it.
This data gets embedded and is stored in a milvus vector DB.

### Curated data (curated)
The curated data crawler also crawls websites and documents,
but in a predefined structured way. It is highly specific to the data source it crawls.
That way it can get events, news, information about people and much more,
convert it to structured data, that is then stored inside a sqlite database.

### Tools (tools)
The tools folder holds utility functions and modules that are used for crawling data.
They are not specific to any data source and can be used by both crawlers.
Thinks like:
- crawling a website
- turn html into markdown
- embed text
- read and write data from and to a sqlite database
- read and write data from a to the milvus database