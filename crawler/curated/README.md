## 🗃️ **Daten-Typen für die kuratierte Datenbank**

### 1. 🗓️ **Events / Veranstaltungen**
Perfekt geeignet, weil:
- immer gleiche Struktur (Datum, Ort, Titel)
- oft gesucht: „Was ist am Wochenende los?“

**Datenfelder:**
| Feld             | Beschreibung                         |
|------------------|--------------------------------------|
| `id`             | Eindeutiger Bezeichner               |
| `title`          | Veranstaltungstitel                  |
| `description`    | Kurzbeschreibung                     |
| `date`           | Datum der Veranstaltung (ISO 8601)   |
| `time`           | Uhrzeit (optional, z. B. "19:00")     |
| `location`       | Ort (z. B. "Mehrzweckhalle")         |
| `organizer`      | Veranstalter (z. B. "Musikverein")   |
| `tags`           | z. B. ["Musik", "Feuerwehr", "Kinder"] |
| `url`            | <PERSON> zur Originalquelle              |
| `image_url`      | Bild (falls vorhanden)               |
| `last_updated`   | Für <PERSON>/Sync-Funk<PERSON>              |

---

### 2. 👤 **Personen / Ansprechpartner**
Sehr sinnvoll bei:
- Vereinsfunktionäre
- Gemeindebedienstete
- Bürgermeister, Vize, Amtsleiter etc.

**Datenfelder:**
| Feld             | Beschreibung                         |
|------------------|--------------------------------------|
| `id`             | Eindeutiger Schlüssel                |
| `name`           | Vor- & Nachname                      |
| `role`           | z. B. "Obmann", "Bürgermeisterin"     |
| `organization`   | Verein / Amt / Abteilung             |
| `email`          | Kontakt (optional anonymisierbar)   |
| `phone`          | Telefonnummer                        |
| `public`         | true/false → für Datenschutzsteuerung |
| `url`            | Link zum öffentlichen Profil         |
| `photo_url`      | (optional) Profilbild                |
| `last_updated`   | Für Änderungen                       |

---

### 3. 🏢 **Organisationen / Vereine / Institutionen**
Hilfreich für:
- Suche nach Vereinen, Öffnungszeiten, Ansprechpersonen
- Zentrale Übersicht, auch für Veranstaltungen

**Datenfelder:**
| Feld             | Beschreibung                         |
|------------------|--------------------------------------|
| `id`             | z. B. `"verein_musikverein"`         |
| `name`           | Offizieller Name                     |
| `type`           | z. B. "Feuerwehr", "Sportverein"     |
| `description`    | Zweck & Tätigkeitsbereich            |
| `contact_email`  | E-Mail (optional)                    |
| `phone`          | Telefon                              |
| `address`        | Adresse                              |
| `url`            | Link zur Website                     |
| `members_count`  | (optional) Anzahl Mitglieder          |
| `last_updated`   | Aktueller Stand                      |

---

### 4. 📰 **News / Bekanntmachungen**
Zwar auch embedbar, aber strukturierte Erfassung macht Sinn bei:
- Müllabfuhränderungen
- Straßensperren
- Wahlinfos

**Datenfelder:**
| Feld             | Beschreibung                         |
|------------------|--------------------------------------|
| `id`             | z. B. `"news_20250415_muell"`        |
| `title`          | Titel der Meldung                    |
| `body`           | Text                                |
| `published_date` | Datum der Veröffentlichung           |
| `category`       | z. B. "Abfall", "Wahl", "Sperre"     |
| `tags`           | optional                             |
| `url`            | Quelle                               |
| `last_updated`   | Für Synchronisierung                 |

---

### 5. 🧾 **Formulare / Amtswege**
Sehr hilfreich für "Wie kann ich X machen?"-Fragen:

**Datenfelder:**
| Feld             | Beschreibung                         |
|------------------|--------------------------------------|
| `id`             | `"form_hundemeldung"`                |
| `title`          | z. B. "Hund anmelden"                |
| `description`    | Was passiert hier?                   |
| `steps`          | Liste von Anweisungen                |
| `required_docs`  | Was muss man mitbringen              |
| `fees`           | Falls relevant                       |
| `duration`       | Dauer der Bearbeitung (optional)     |
| `contact`        | Zuständiges Amt                      |
| `url`            | Link zum PDF oder Online-Formular    |
| `last_updated`   | Für Aktualität                       |

---

## 📌 Fazit: Für den Start am besten geeignet

| Priorität | Typ             | Warum?                                           |
|----------|------------------|--------------------------------------------------|
| 🟢 Hoch   | Events           | Häufige Fragen, sehr strukturiert                |
| 🟢 Hoch   | Personen         | Ansprechpartner, wichtig für lokale Orientierung |
| 🟡 Mittel | Organisationen   | Ergänzt Personen & Events                        |
| 🟡 Mittel | News             | Nützlich, wenn regelmäßig gepflegt               |
| 🟡 Mittel | Formulare        | Gute Ergänzung für Service-orientierte Anfragen  |
