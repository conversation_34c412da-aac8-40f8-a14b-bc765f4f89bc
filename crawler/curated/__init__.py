from .gemeinde_news import gemeinde_news
from .people_knowledge import generate_people_knowledge
from .organisation_knowledge import generate_organisation_knowledge

# Crawl a list of specific datasources and generate structured data
def crawl():
    gemeinde_news()

    # Generate combined people knowledge from all crawled data
    generate_people_knowledge()

    # Generate combined organisation knowledge from all crawled data
    generate_organisation_knowledge()