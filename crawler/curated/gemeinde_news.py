import json
import os
from bs4 import BeautifulSoup, Tag
import requests
from pydantic import BaseModel
from crawler.tools import tmp_crawl_path
from openai import OpenAI
client = OpenAI()

def gemeinde_news():
    raw_path = os.path.join(tmp_crawl_path, "raw")
    markdown_path = os.path.join(tmp_crawl_path, "markdown")
    classification_path = os.path.join(tmp_crawl_path, "classification")
    embedding_path = os.path.join(tmp_crawl_path, "embedding")

    if(os.path.exists(raw_path) is False):
        os.makedirs(raw_path, exist_ok=True)
        aktuelles_html = requests.get('http://www.zelking-matzleinsdorf.gv.at/aktuell.html')
        aktuelles_soup = BeautifulSoup(aktuelles_html.text, 'html.parser')
        aktuelles_content = aktuelles_soup.select_one('#column2')
        rawnews = split_by_h2(aktuelles_content)

        for i, news in enumerate(rawnews):
            with open(os.path.join(raw_path, f"aktuelles_{i}.md"), "w") as f:
                # write the newsitem as json
                f.write(news)

    else:
        print("Skipping crawling because the raw data already exists")

def split_by_h2(html_content: Tag) -> list[str]:
    current_headline = None
    current_block = []
    blocks: list[str] = []

    soup = html_content

    for tag in soup.find_all():
        if tag.name != "h2":
            tag.unwrap()  # Removes the tag but keeps its content
    
    for tag in soup.children:  # Iterate through h2 and p tags
        if tag.name == "h2":
            if current_block and current_headline:  # Save previous block if not empty
                if len(current_block) > 10 and len(current_headline) > 10:
                    blocks.append(f"# {current_headline}\n\n{'<br />'.join(current_block)}")
                current_block = []  # Reset for new section
            current_headline = tag.text
        else:
            current_block.append(tag.text)  # Collect paragraphs
    
    return blocks

# gemeinde_news()