from typing import Optional

from pydantic import BaseModel

class BasicNewsItem(BaseModel):
    title: str
    content: str
    date: Optional[str]
    location: Optional[str]

class SimpleNewsItem(BasicNewsItem):
    source: str
    sourceType: str

class BasicEvent(BaseModel):
    title: str
    description: str
    date: str
    time: Optional[str]
    location: str

class SimpleEvent(BasicEvent):
    source: str
    sourceType: str

class PersonKnowledge(BaseModel):
    lastName_firstName: str
    facts: list[str]