import os
import json
from typing import Dict, List, Optional
from openai import OpenAI
from pydantic import BaseModel
from crawler.tools import data_repo_path
from crawler.tools.classification_model import OrganisationKnowledge

class PersonClassification(BaseModel):
    """Model for representing potential duplicate person entries."""
    original: str
    is_person: bool
    firstname: Optional[str]
    lastname: Optional[str]
    title: Optional[str]

class PersonClassificationResponse(BaseModel):
    """Response model for the OpenAI duplicate detection API."""
    results: List[PersonClassification]

class KnowledgeWithSource(BaseModel):
    source: str
    knowledge: list[str]

class OrganisationKnowledgeWithSource(BaseModel):
    name: str
    knowledge_sentences_about_that_organisation: list[str]

class OrganisationKnowledge(BaseModel):
    name: str
    knowledge: str

def generate_organisation_knowledge():
    """
    Generate a combined organisation knowledge dictionary from all crawled data.

    This function:
    1. Gets all subfolders from raw_data_path
    2. Processes all JSON files in these folders to extract people_knowledge
    3. Combines all people with the same firstname+lastname
    4. Uses OpenAI to identify duplicates and non-people entries
    5. Merges duplicates and removes non-people entries
    6. Saves the final dictionary to data_repo_path/people_knowledge/data.json
    """
    raw_data_path = os.path.join(data_repo_path, 'raw')

    # Dictionary to store combined people data
    # Key: firstname_lastname
    # Value: PeopleKnowledge object
    combined_organisations: Dict[str, list[str]] = {}

    # Get all subfolders in raw_data_path
    subfolders = [f for f in os.listdir(raw_data_path)
                 if os.path.isdir(os.path.join(raw_data_path, f))]

    print(f"Found {len(subfolders)} subfolders in {raw_data_path}")

    # Process each subfolder
    for subfolder in subfolders:
        subfolder_path = os.path.join(raw_data_path, subfolder)

        # Get all JSON files in the subfolder
        json_files = [f for f in os.listdir(subfolder_path)
                     if f.endswith('.json')]

        print(f"Processing {len(json_files)} JSON files in {subfolder}")

        # Process each JSON file
        for json_file in json_files:
            file_path = os.path.join(subfolder_path, json_file)

            with open(file_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)

            # Check if the file contains people_knowledge
            if 'organisation_knowledge' not in metadata or metadata['organisation_knowledge'] is None:
                continue

            organisation_knowledge: list[OrganisationKnowledgeWithSource] = metadata['organisation_knowledge']

            # Process each person
            for single_organisation_knowledge in organisation_knowledge:
                # Create a key using firstname and lastname
                if 'name' not in single_organisation_knowledge or single_organisation_knowledge['name'] is None:
                    continue  # Skip entries without firstname or lastname

                name = single_organisation_knowledge['name']
                if name is None:
                    continue

                key = name

                # Get knowledge sentences
                knowledge_sentences: list[str] = single_organisation_knowledge['knowledge_sentences_about_that_organisation']

                if key in combined_organisations:
                    combined_organisations[key].extend(knowledge_sentences)
                else:
                    # Create new PeopleKnowledge object
                    combined_organisations[key] = knowledge_sentences.copy()

    print(f"Combined data for {len(combined_organisations)} organisations")

    orga_names = [key for key, org in combined_organisations.items()]
    print(orga_names)

    # TODO: A lot of duplicates and unnecessary items like "Facebook"

    return

    # Use OpenAI to identify duplicates and non-people entries
    # duplicates_and_non_people = identify_people_and_non_people(people_names)

    # Process the results from OpenAI
    # final_people = process_duplicates_and_non_people(combined_people, duplicates_and_non_people, people_names)
    final_organisation_dict = combined_organisations

    # Save the final dictionary
    target_dir = os.path.join(data_repo_path, 'organisation_knowledge')
    os.makedirs(target_dir, exist_ok=True)

    target_file = os.path.join(target_dir, 'data.json')

    # Convert to dictionary for JSON serialization
    final_organisation_array: List[OrganisationKnowledge] = []
    for key, knowledge_sentences in final_organisation_dict.items():
        knowledge = generate_summary(key, knowledge_sentences)

        print(f"Generated knowledge for {key}")

        final_organisation_array.append(OrganisationKnowledge(
            name=key,
            knowledge=knowledge
        ).model_dump())

    with open(target_file, 'w', encoding='utf-8') as f:
        json.dump(final_organisation_array, f, indent=2)

    print(f"Saved combined people knowledge to {target_file}")

def generate_summary(name: str, knowledge: list[str]) -> str:
    # Initialize OpenAI client
    client = OpenAI()

    # Create the prompt for OpenAI
    systemmessage = """
    You are an editor and help writers with resports about organisations.
    The user gives you a text which contains of several sentences about an organisation.
    Some of them are general knowledge, some of them refer to specific events or documents.
    Your job is to extract the general knowledge and create a report for the public to read about that organisation.
    It should be relevant information for the local inhabitants of Zelking-Matzleinsdorf and should skip any information that is not relevant for them.
    Do not include any introduction or final words. Just the pure report.
    The response must be in german.
    """

    prompt = """
    Here is the current knowledge of {}:
    
    {}
    """.format(name, "\n".join(knowledge))

    try:
        print('Call openai')
        # Call OpenAI API using parse method
        response = client.chat.completions.create(
            model="gpt-4.1-mini",
            messages=[
                {"role": "system", "content": systemmessage},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3
        )
        print(f"Got response from openai for {name}: {response.choices[0].message.content}")

        # Get the parsed response
        return response.choices[0].message.content

    except Exception as e:
        print(f"Error calling OpenAI API: {str(e)}")
        return ""