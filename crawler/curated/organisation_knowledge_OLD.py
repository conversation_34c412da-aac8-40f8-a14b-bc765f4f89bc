import os
import json
from typing import Dict, List, Optional
from openai import OpenAI
from pydantic import BaseModel
from crawler.tools import data_repo_path
from crawler.tools.classification_model import OrganisationKnowledge

class OrganisationGroups(BaseModel):
    """Response model for the OpenAI duplicate detection API."""
    results: List[List[str]]

class KnowledgeWithSource(BaseModel):
    source: str
    knowledge: list[str]

class OrganisationKnowledgeWithSource(BaseModel):
    name: str
    knowledge_sentences_about_that_organisation: list[KnowledgeWithSource]

def generate_organisation_knowledge():
    """
    Generate a combined organisation knowledge dictionary from all crawled data.

    This function:
    1. Gets all subfolders from raw_data_path
    2. Processes all JSON files in these folders to extract people_knowledge
    3. Combines all people with the same firstname+lastname
    4. Uses OpenAI to identify duplicates and non-people entries
    5. Merges duplicates and removes non-people entries
    6. Saves the final dictionary to data_repo_path/people_knowledge/data.json
    """
    raw_data_path = os.path.join(data_repo_path, 'raw')

    # Dictionary to store combined organisation data
    # Key: name
    # Value: OrganisationKnowledge object
    combined_organisations: Dict[str, OrganisationKnowledgeWithSource] = {}

    # Get all subfolders in raw_data_path
    subfolders = [f for f in os.listdir(raw_data_path)
                 if os.path.isdir(os.path.join(raw_data_path, f))]

    print(f"Found {len(subfolders)} subfolders in {raw_data_path}")

    # Process each subfolder
    for subfolder in subfolders:
        subfolder_path = os.path.join(raw_data_path, subfolder)

        # Get all JSON files in the subfolder
        json_files = [f for f in os.listdir(subfolder_path)
                     if f.endswith('.json')]

        print(f"Processing {len(json_files)} JSON files in {subfolder}")

        # Process each JSON file
        for json_file in json_files:
            file_path = os.path.join(subfolder_path, json_file)

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)

                # Check if the file contains organisation_knowledge
                if 'organisation_knowledge' not in metadata or metadata['organisation_knowledge'] is None:
                    continue

                organisation_knowledge = metadata['organisation_knowledge']

                # Process each person
                for knowledge in organisation_knowledge:
                    # Create a key using firstname and lastname
                    name = knowledge.get('name', '').strip()

                    if not name:
                        continue  # Skip entries without firstname or lastname

                    # Get knowledge sentences
                    knowledge_sentences = knowledge.get('knowledge_sentences_about_that_organisation', [])

                    if name in combined_organisations:
                        combined_organisations[name].knowledge_sentences_about_that_organisation.append(KnowledgeWithSource(
                            source=json_file,
                            knowledge=knowledge_sentences
                        ))
                    else:
                        # Create new OrganisationKnowledge object
                        combined_organisations[name] = OrganisationKnowledgeWithSource(
                            name=name,
                            knowledge_sentences_about_that_organisation=[KnowledgeWithSource(
                                source=json_file,
                                knowledge=knowledge_sentences
                            )]
                        )

            except Exception as e:
                print(f"Error processing {file_path}: {str(e)}")

    print(f"Combined data for {len(combined_organisations)} people")

    organisation_names = [key for key, org in combined_organisations.items()]
    name_groups = identify_duplicates_and_clean(organisation_names=organisation_names)

    final_organisations: Dict[str, OrganisationKnowledgeWithSource] = {}
    for group in name_groups:
        main_name = group[0]
        main_item = combined_organisations.get(main_name)
        final_organisations[main_name] = main_item

        for merge_key in group[1:]:
            merge_item = combined_organisations.get(merge_key)
            if 'knowledge_sentences_about_that_organisation' not in merge_item:
                continue
            main_item.knowledge_sentences_about_that_organisation.extend(merge_item.knowledge_sentences_about_that_organisation)

    # Save the final dictionary
    target_dir = os.path.join(data_repo_path, 'organisation_knowledge')
    os.makedirs(target_dir, exist_ok=True)

    target_file = os.path.join(target_dir, 'data.json')

    # Convert to dictionary for JSON serialization
    final_people_dict = [person.model_dump() for key, person in final_organisations.items()]

    with open(target_file, 'w', encoding='utf-8') as f:
        json.dump(final_people_dict, f, indent=2)

    print(f"Saved combined organisation knowledge to {target_file}")

def identify_duplicates_and_clean(organisation_names: List[str]) -> List[List[str]]:
    """
    Use OpenAI to identify duplicates and non-people entries.

    Args:
        people_names: List of people names (title + firstname + lastname)

    Returns:
        List of PersonDuplicate objects with information about duplicates and non-people
    """
    if not organisation_names:
        return []

    print("Identifying duplicates and non-people entries using OpenAI...")

    # Initialize OpenAI client
    client = OpenAI()

    # Create the prompt for OpenAI
    systemmessage = """
    You are an assitant for cleaning data.
    I have a list of organisation_names, that might contains duplicates with a slight variation.
    You must group possible duplicates in a list and return a list of lists in the end.
    Entries that don't have any duplicates are stored as just a list with this one entry.

    Also remove items that do not refer to a local organisation.

    Here is an example:
    
    - FF Mannersdorf
    - FF Zelking
    - Freiwillige Feuerwehr Mannersdorf
    - Feuerwehr Mannersdorf
    - Feuerwehr Zelking
    - Gemeinde
    - HLF3
    - Facebook

    This will result in this list:
    [
        [
            'FF Mannersdorf',
            'Freiwillige Feuerwehr Mannersdorf',
            'Feuerwehr Mannersdorf'
        ],
        [
            'FF Zelking',
            'Feuerwehr Zelking'
        ],
        [
            'Gemeinde'
        ]
    ]

    HLF3 and Facebook are not local organisation
    """

    prompt = """
    Here is a list of organisations:
    
    {}
    """.format("\n".join(organisation_names))

    try:
        print('Call openai')
        # Call OpenAI API using parse method
        response = client.beta.chat.completions.parse(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": systemmessage},
                {"role": "user", "content": prompt}
            ],
            temperature=0.0,  # Use deterministic output
            response_format=OrganisationGroups,
        )
        print('Get response from openai')

        # Get the parsed response
        parsed_response = response.choices[0].message.parsed
        print(parsed_response)

        # Extract the results
        return parsed_response.results

    except Exception as e:
        print(f"Error calling OpenAI API: {str(e)}")
        return []
