import os
import json
from typing import Dict, List, Optional
from openai import OpenAI
from pydantic import BaseModel
from crawler.tools import data_repo_path
from crawler.tools.classification_model import OrganisationKnowledge

class PersonClassification(BaseModel):
    """Model for representing potential duplicate person entries."""
    original: str
    is_person: bool
    firstname: Optional[str]
    lastname: Optional[str]
    title: Optional[str]

class PersonClassificationResponse(BaseModel):
    """Response model for the OpenAI duplicate detection API."""
    results: List[PersonClassification]

class KnowledgeWithSource(BaseModel):
    source: str
    knowledge: list[str]

class PeopleKnowledgeWithSource(BaseModel):
    firstname: str
    lastname: str
    title: Optional[str]
    knowledge_sentences_about_that_person: list[KnowledgeWithSource]

class OrganisationKnowledge(BaseModel):
    firstname: str
    lastname: str
    title: Optional[str]
    knowledge: str

def generate_people_knowledge():
    """
    Generate a combined people knowledge dictionary from all crawled data.

    This function:
    1. Gets all subfolders from raw_data_path
    2. Processes all JSON files in these folders to extract people_knowledge
    3. <PERSON><PERSON>ines all people with the same firstname+lastname
    4. Uses OpenAI to identify duplicates and non-people entries
    5. Merges duplicates and removes non-people entries
    6. Saves the final dictionary to data_repo_path/people_knowledge/data.json
    """
    raw_data_path = os.path.join(data_repo_path, 'raw')

    # Dictionary to store combined people data
    # Key: firstname_lastname
    # Value: PeopleKnowledge object
    combined_people: Dict[str, PeopleKnowledgeWithSource] = {}

    # Get all subfolders in raw_data_path
    subfolders = [f for f in os.listdir(raw_data_path)
                 if os.path.isdir(os.path.join(raw_data_path, f))]

    print(f"Found {len(subfolders)} subfolders in {raw_data_path}")

    # Process each subfolder
    for subfolder in subfolders:
        subfolder_path = os.path.join(raw_data_path, subfolder)

        # Get all JSON files in the subfolder
        json_files = [f for f in os.listdir(subfolder_path)
                     if f.endswith('.json')]

        print(f"Processing {len(json_files)} JSON files in {subfolder}")

        # Process each JSON file
        for json_file in json_files:
            file_path = os.path.join(subfolder_path, json_file)

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)

                # Check if the file contains people_knowledge
                if 'people_knowledge' not in metadata or metadata['people_knowledge'] is None:
                    continue

                people_knowledge = metadata['people_knowledge']

                # Process each person
                for person_knowledge in people_knowledge:
                    # Create a key using firstname and lastname
                    firstname = person_knowledge.get('firstname', '').strip()
                    lastname = person_knowledge.get('lastname', '').strip()
                    source = metadata.get('source', '').strip()

                    if not firstname or not lastname:
                        continue  # Skip entries without firstname or lastname

                    key = f"{firstname} {lastname}"

                    # Get knowledge sentences
                    knowledge_sentences = person_knowledge.get('knowledge_sentences_about_that_person', [])

                    if key in combined_people:
                        combined_people[key].knowledge_sentences_about_that_person.append(KnowledgeWithSource(
                            source=source,
                            knowledge=knowledge_sentences
                        ))
                    else:
                        # Create new PeopleKnowledge object
                        combined_people[key] = PeopleKnowledgeWithSource(
                            firstname=firstname,
                            lastname=lastname,
                            title=person_knowledge.get('title'),
                            knowledge_sentences_about_that_person=[KnowledgeWithSource(
                                source=source,
                                knowledge=knowledge_sentences
                            )]
                        )

            except Exception as e:
                print(f"Error processing {file_path}: {str(e)}")

    print(f"Combined data for {len(combined_people)} people")

    # Create a list of all people names for OpenAI analysis
    people_names = []
    for key, person in combined_people.items():
        name_parts = []
        if person.title:
            name_parts.append(person.title)
        name_parts.append(person.firstname)
        name_parts.append(person.lastname)
        people_names.append(" ".join(name_parts))

    # Use OpenAI to identify duplicates and non-people entries
    # duplicates_and_non_people = identify_people_and_non_people(people_names)

    # Process the results from OpenAI
    # final_people = process_duplicates_and_non_people(combined_people, duplicates_and_non_people, people_names)
    final_people_dict = combined_people

    # Save the final dictionary
    target_dir = os.path.join(data_repo_path, 'people_knowledge')
    os.makedirs(target_dir, exist_ok=True)

    target_file = os.path.join(target_dir, 'data.json')

    # Convert to dictionary for JSON serialization
    final_people_array: List[OrganisationKnowledge] = []
    for key, person in final_people_dict.items():
        knowledge = generate_person_summary(person)

        print(f"Generated knowledge for {person.firstname} {person.lastname}\n\n{knowledge}")

        final_people_array.append(OrganisationKnowledge(
            firstname=person.firstname,
            lastname=person.lastname,
            title=person.title,
            knowledge=knowledge
        ).model_dump())

    with open(target_file, 'w', encoding='utf-8') as f:
        json.dump(final_people_array, f, indent=2)

    print(f"Saved combined people knowledge to {target_file}")

def generate_person_summary(knowledge: PeopleKnowledgeWithSource) -> str:
    # Initialize OpenAI client
    client = OpenAI()

    # Create the prompt for OpenAI
    systemmessage = """
    You are an editor and help writers with biographies.
    The user gives you a text which contains of several sentences about the person.
    Some of them are general knowledge, some of them refer to specific events or documents.
    Your job is to extract the general knowledge and create a biography for the public to read about that person.
    It should be relevant information for the local inhabitants of Zelking-Matzleinsdorf and should skip any information that is not relevant for them.
    Do not include any introduction or final words. Just the pure biography.
    The response must be in german.
    """

    entries: List[str] = []
    for knowledge_entry in knowledge.knowledge_sentences_about_that_person:
        for knowledge_sentence in knowledge_entry.knowledge:
            entries.append(f"{knowledge_entry.source}:\n{knowledge_sentence}")

    prompt = """
    Here is the current knowledge of {}:
    
    {}
    """.format(f"{knowledge.firstname} {knowledge.lastname}", "\n\n".join(entries))

    try:
        print('Call openai')
        # Call OpenAI API using parse method
        response = client.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": systemmessage},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3
        )
        print('Get response from openai')

        # Get the parsed response
        return response.choices[0].message.content

    except Exception as e:
        print(f"Error calling OpenAI API: {str(e)}")
        return ""