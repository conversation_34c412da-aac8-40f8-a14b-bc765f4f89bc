from crawler.raw.gemeinderat import crawl_gemeinderat
from .gemeinde import crawl_gemeinde
from .utc import crawl_utc
from .ffmatzleinsdorf import crawl_ffmatzleinsdorf
from .ffmannersdorf import crawl_ffmannersdorf
from .ffzelking import crawl_ffzelking
from .facebook import crawl_facebook

# The main entry point that triggers all the crawlers
def crawl():
    # crawl the website http://www.zelking-matzleinsdorf.gv.at/
    # This is the main website of the Gemeinde Zelking-Matzleinsdorf
    crawl_gemeinde()

    crawl_gemeinderat()
    
    # crawl the website https://www.utczelking.at/
    # This is the website of the Tennisclub Zelking
    # crawl_utc()

    # crawl the website https://www.ff-matzleinsdorf.at/
    # This is the website of the Feuerwehr Matzleinsdorf
    crawl_ffmatzleinsdorf()
    
    # crawl the website https://www.ff-mannersdorf.co.at/
    # This is the website of the Feuerwehr Mannersdorf
    crawl_ffmannersdorf()
    
    # crawl the website https://www.ffzelking.com/
    # This is the website of the Feuerwehr Zelking
    crawl_ffzelking()

    # crawl the facebook group https://www.facebook.com/groups/zelkingmatzleinsdorf/
    # This is the Facebook group of the Gemeinde Zelking-Matzleinsdorf
    # crawl_facebook()