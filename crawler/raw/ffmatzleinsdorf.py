import os
import shutil

from urllib import parse
from crawler.tools.crawl_urls import crawl_and_build_tree
from crawler.tools.sanitize_html import sanitize_html
from crawler.tools import tmp_crawl_path, data_repo_path
from crawler.tools.markdown import convert_files_to_markdown_after_crawl_step
from crawler.tools.converter import convert_all_doc_to_docx
from crawler.tools.classification import classify_from_raw_markdown
from crawler.tools.embedding import embed_documents_after_markdown_step
from crawler.tools.data_repo import move_raw_crawled_data_to_data_repo

def ff_matzleinsdorf_crawl_guard(url: str, mimetype: str, html_content: str):
    # if the url contains 'category' or the first path element is a number, skip it

    ignoredparts = ['category', 'login', 'sitemap']
    parsed_url = parse.urlparse(url)
    pathparts = parsed_url.path.split("/")
    if len(pathparts) > 1:
        firstpathpart = parsed_url.path.split("/")[1]
    else:
        firstpathpart = ""

    print(f"Guard: first part = {url} | {parsed_url.path} | {firstpathpart}")

    if firstpathpart in ignoredparts:
        return False

    supported_mimetypes = ['application/pdf', 'application/msword', 'text/html']

    if mimetype not in supported_mimetypes:
        return False

    return True

def crawl_ffmatzleinsdorf():
    target_dir = os.path.join(data_repo_path, 'raw', 'ffmatzleinsdorf')
    if os.path.exists(target_dir):
        print('Skipping ffmatzleinsdorf')
        return


    raw_path = os.path.join(tmp_crawl_path, "raw")
    markdown_path = os.path.join(tmp_crawl_path, "markdown")
    classification_path = os.path.join(tmp_crawl_path, "classification")
    embedding_path = os.path.join(tmp_crawl_path, "embedding")

    # Crawl the website and store the raw data
    if(os.path.exists(raw_path) is False):
        crawl_and_build_tree("https://www.ff-matzleinsdorf.at/", raw_path, 5, ff_matzleinsdorf_crawl_guard)
    else:
        print("Skipping crawling because the raw data already exists")
    
    # convert all doc to docx files
    if(os.path.exists(markdown_path) is False):
        convert_all_doc_to_docx(raw_path)
    else:
        print("Skipping converting doc to docx because the markdown data already exists")

    # Sanitize HTML files
    if(os.path.exists(markdown_path) is False):
        sanitize_html(
            raw_path,
            ['.tm-content'],
            ["script", "iframe", "figure", "img", '.gallery']
        )
    else:
        print("Skipping sanitizing because the markdown data already exists")

    # Convert all files to Markdown
    if(os.path.exists(markdown_path) is False):
        convert_files_to_markdown_after_crawl_step(raw_path, markdown_path)
    else:
        print("Skipping converting because the markdown data already exists")

    # Classification of the extracted data
    if(os.path.exists(classification_path) is False):
        classify_from_raw_markdown(markdown_path, classification_path)
    else:
        print("Skipping classification because the classification data already exists")

    # Embedding of the data
    if(os.path.exists(embedding_path) is False):
        embed_documents_after_markdown_step(classification_path, embedding_path)
    else:
        print("Skipping embedding because the embedding data already exists")

    move_raw_crawled_data_to_data_repo(target_dir)

    # delete the tmp_crawler folder
    shutil.rmtree(tmp_crawl_path)

    pass

crawl_ffmatzleinsdorf()
