import os
import shutil
import requests
import time
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup
from crawler.tools.classification_model import ClassificationMetadata
from crawler.tools.crawl_urls_model import CrawlMetadata
from crawler.tools.markdown_model import MarkdownMetadata
from crawler.tools.sanitize_html import sanitize_html
from crawler.tools import tmp_crawl_path
from crawler.tools.markdown import convert_files_to_markdown_after_crawl_step
from crawler.tools.converter import convert_all_doc_to_docx
from crawler.tools.classification import ClassificationResponse, classify_from_raw_markdown, classification_system_prompt
from crawler.tools.embedding import embed_documents_after_markdown_step
from crawler.tools.data_repo import move_raw_crawled_data_to_data_repo
from crawler.tools import tmp_crawl_path, data_repo_path
from openai import OpenAI

client = OpenAI()

def crawl_protocolls(target_folder_path: str, delay: float = 0.5):
    """
    Crawl the protocols page of the Gemeinderat website and download all linked files.

    Args:
        target_folder_path: Path where the downloaded files will be saved
        delay: Delay between requests in seconds to avoid overloading the server
    """
    # Ensure the target folder exists
    os.makedirs(target_folder_path, exist_ok=True)

    # URL of the protocols page
    url = "http://www.zelking-matzleinsdorf.gv.at/protokolle.html"
    base_url = "http://www.zelking-matzleinsdorf.gv.at/"

    print(f"Crawling protocols from {url}")

    # Get the page content
    response = requests.get(url)
    if response.status_code != 200:
        print(f"Failed to get the protocols page: {response.status_code}")
        return

    # Parse the HTML with BeautifulSoup
    soup = BeautifulSoup(response.text, "html.parser")

    # Find all elements with class "pdfLink" or "wordLink"
    pdf_links = soup.find_all(class_="pdfLink")
    word_links = soup.find_all(class_="wordLink")

    # Combine all link containers
    link_containers = pdf_links + word_links

    # Keep track of downloaded files to avoid duplicates
    downloaded_files = set()

    # Process each link container
    for container in link_containers:
        # Find all <a> elements in the container
        links = container.find_all("a", href=True)

        for link in links:
            href = link["href"]

            # Construct the full URL if it's a relative path
            if not href.startswith("http"):
                file_url = urljoin(base_url, href)
            else:
                file_url = href

            # Extract filename from the URL
            filename = os.path.basename(file_url)

            # Skip if we've already downloaded this file
            if filename in downloaded_files:
                print(f"Skipping already downloaded file: {filename}")
                continue

            print(f"Downloading {file_url} to {filename}")

            try:
                # Add delay between requests to avoid overloading the server
                time.sleep(delay)

                # Download the file
                file_response = requests.get(file_url)

                if file_response.status_code == 200:
                    # Save the file to the target folder
                    file_path = os.path.join(target_folder_path, filename)
                    with open(file_path, "wb") as f:
                        f.write(file_response.content)

                    # Add to the set of downloaded files
                    downloaded_files.add(filename)
                    print(f"Successfully downloaded {filename}")
                else:
                    print(f"Failed to download {file_url}: {file_response.status_code}")

            except Exception as e:
                print(f"Error downloading {file_url}: {str(e)}")

    print(f"Finished crawling protocols. Downloaded {len(downloaded_files)} files.")

def generate_raw_metadata_for_downloaded_files(target_folder_path: str):
    all_files = os.listdir(target_folder_path)

    for file in all_files:
        file_path = os.path.join(target_folder_path, file)
    
        # Determine the mimetype
        doc_type = "pdf"
        mimetype = 'application/pdf'
        if file.endswith('.doc') or file.endswith('.DOC') or file.endswith('.docx'):
            mimetype = 'application/msword'
            doc_type = "word"
        elif file.endswith('.html') or file.endswith('.htm'):
            mimetype = 'text/html'
            doc_type = "html"

        # Create metadata using the CrawlMetadata model
        metadata = CrawlMetadata.create(
            filename=file,
            mimetype=mimetype,
            url="http://www.zelking-matzleinsdorf.gv.at/protokolle.html",
            doc_type=doc_type
        )

        # Save metadata to JSON file
        json_path = f"{file_path}.json"
        with open(json_path, "w", encoding="utf-8") as f:
            json.dump(metadata.model_dump(), f, indent=2)

split_and_classification_system_prompt = """
{}

Another task is to split the documents based on the Tagesordnung, which is the list of topics that are discussed in the document.
The first chunk should be the introduction up to (and including) the Tagesordnung.
Next are the chunks split by the Tagesordnung.
Return the chunks as a list of strings in the chunks property.
Do not shorten or summarize the chunks. They should be keep as they are in the original document.
""".format(classification_system_prompt)

def split_protokoll_and_classify(markdown_path: str, target_path: str):
    # Ensure the target folder exists
    os.makedirs(target_path, exist_ok=True)

    json_files = [f for f in os.listdir(markdown_path) if f.endswith('.json')]

    print(json_files)

    for index, json_file in enumerate(json_files):
        with open(os.path.join(markdown_path, json_file), "r") as f:
            # metadata_json = json.load(os.path.join(markdown_path, json_file))
            metadata_json = json.load(f)
            metadata = MarkdownMetadata.model_validate(metadata_json)

        markdown_file = metadata.markdown_file

        # open the file
        with open(os.path.join(markdown_path, markdown_file), "r") as f:
            print(json_file)
            content = f.read()

            response = client.beta.chat.completions.parse(
                model="gpt-4.1-mini",
                messages=[
                    {"role": "system", "content": split_and_classification_system_prompt},
                    {"role": "user", "content": content},
                ],
                temperature=0,
                response_format=ClassificationResponse,
            )

            classification = response.choices[0].message.parsed
            
            # Ensure we have proper dictionaries for nested models
            if classification.organisation_knowledge:
                # Convert any OrganisationKnowledge objects to dictionaries if needed
                org_knowledge_list = []
                for org in classification.organisation_knowledge:
                    if isinstance(org, dict):
                        org_knowledge_list.append(org)
                    else:
                        org_knowledge_list.append(org.model_dump())
                classification.organisation_knowledge = org_knowledge_list
                
            if classification.people_knowledge:
                # Convert any PeopleKnowledge objects to dictionaries if needed
                people_knowledge_list = []
                for person in classification.people_knowledge:
                    if isinstance(person, dict):
                        people_knowledge_list.append(person)
                    else:
                        people_knowledge_list.append(person.model_dump())
                classification.people_knowledge = people_knowledge_list

            chunks_list = []
            if classification.chunks:
                # Convert any chunk objects to strings if needed
                for i, chunk in enumerate(classification.chunks):
                    chunk_filename = f"{markdown_file[:-3]}_chunk_{i}.md"
                    markdown_chunk_path = os.path.join(target_path, chunk_filename)
                    chunks_list.append(chunk_filename)
                    with open(markdown_chunk_path, 'w', encoding='utf-8') as f:
                        f.write(f"# Gemeinderatsitzung - ({classification.date}) - {classification.title}\n\n{chunk}")

            # Create a ClassificationMetadata object
            classification_metadata = ClassificationMetadata.create_from_markdown_metadata(
                markdown_metadata=metadata,
                date=classification.date,
                title=classification.title,
                description=classification.description,
                people_knowledge=classification.people_knowledge,
                organisation_knowledge=classification.organisation_knowledge,
                markdown_chunk_files=chunks_list
            )

            # Save the ClassificationMetadata to a JSON file in the target directory
            target_json_path = os.path.join(target_path, json_file)
            with open(target_json_path, 'w', encoding='utf-8') as f:
                json.dump(classification_metadata.model_dump(), f, indent=2)

            # Copy the markdown file to the target directory
            source_markdown_path = os.path.join(markdown_path, markdown_file)
            target_markdown_path = os.path.join(target_path, markdown_file)
            shutil.copy2(source_markdown_path, target_markdown_path)

            print(f"Classified {markdown_file} {index+1}/{len(json_files)}")

def crawl_gemeinderat():
    target_dir = os.path.join(data_repo_path, 'raw', 'gemeinderat')
    if os.path.exists(target_dir):
        print('Skipping gemeinderat')
        return

    raw_path = os.path.join(tmp_crawl_path, "raw")
    markdown_path = os.path.join(tmp_crawl_path, "markdown")
    classification_path = os.path.join(tmp_crawl_path, "classification")
    embedding_path = os.path.join(tmp_crawl_path, "embedding")

    # Crawl the protocols
    if(os.path.exists(raw_path) is False):
        crawl_protocolls(raw_path, delay=0.5)
        generate_raw_metadata_for_downloaded_files(raw_path)
    else:
        print("Skipping crawling because the raw data already exists")

    # convert all doc to docx files
    if(os.path.exists(markdown_path) is False):
        convert_all_doc_to_docx(raw_path)
    else:
        print("Skipping converting doc to docx because the markdown data already exists")

    # Convert all files to Markdown
    if(os.path.exists(markdown_path) is False):
        convert_files_to_markdown_after_crawl_step(raw_path, markdown_path)
    else:
        print("Skipping converting because the markdown data already exists")

    # Classification of the extracted data
    if(os.path.exists(classification_path) is False):
        split_protokoll_and_classify(markdown_path, classification_path)
    else:
        print("Skipping classification because the classification data already exists")

    # Embedding of the data
    if(os.path.exists(embedding_path) is False):
        embed_documents_after_markdown_step(classification_path, embedding_path)
    else:
        print("Skipping embedding because the embedding data already exists")

    if(os.path.exists(embedding_path)):
        move_raw_crawled_data_to_data_repo(target_dir)

        # delete the tmp_crawler folder
        # shutil.rmtree(tmp_crawl_path)

    pass