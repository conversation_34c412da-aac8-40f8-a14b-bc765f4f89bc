import os
import json
import shutil
from typing import Optional
from pydantic import BaseModel
from openai import OpenAI
from crawler.tools.markdown_model import MarkdownMetadata
from crawler.tools.classification_model import ClassificationMetadata

class OrganisationKnowledge(BaseModel):
    name: str
    knowledge_sentences_about_that_organisation: list[str]

class PeopleKnowledge(BaseModel):
    firstname: str
    lastname: str
    title: Optional[str] = None
    knowledge_sentences_about_that_person: list[str]

class ClassificationResponse(BaseModel):
    """
    Response model for the OpenAI classification API.
    """
    date: Optional[str] = None
    title: str
    description: Optional[str] = None
    people_knowledge: Optional[list[PeopleKnowledge]] = None
    organisation_knowledge: Optional[list[OrganisationKnowledge]] = None
    chunks: Optional[list[str]]

classification_system_prompt = """
You are an assistant that analyzes documents and extracts metadata.
You will receive a document and need to extract the following information:

1. date: The date the document is referring to. If the document refers to no exact date, check if it is valid from a specific date and use that one. In case it only refers to a year or month, always use the first day of which. If no assignment to a date is possible, leave it empty. Format: YYYY-MM-DD or null.
2. title: A concise, descriptive title for the document. The title should be in German.
3. description: A 100-word summary of the document's content. Only provide a description if the document is longer than 1000 words, otherwise leave it empty. The description should be in German.
4. people_knowledge: Knowledge about people you can get out of the document you just read 
     - firstname: the first name of the person
     - lastname: the last name of the person
     - title: the official title of the person like: Mag., Ing. Mst., ... (optional)
     - knowledge_sentences_about_that_person: A list of general (not event or document specific) sentences that each provide destintive information about this person with full context.
4. organisation_knowledge: Knowledge about organisations (like football clubs, companies, cities) you can get out of the document you just read 
     - name: the name of the organisation
     - knowledge_sentences_about_that_organisation: A list of general (not event or document specific) sentences that each provide a destintive part of information about this organisation with full context.
"""


def classify_from_raw_markdown(source_dir: str, target_dir: str):
    """
    Classify markdown files from a previous markdown conversion step and store them in the target directory.

    This function looks for .json files in the source_dir that contain MarkdownMetadata.
    It reads the markdown file, sends it to OpenAI for classification, and stores
    the result in the target_dir with a new ClassificationMetadata file.

    Args:
        source_dir: Directory containing the .json metadata files and markdown files
        target_dir: Directory where the classified files and new metadata will be stored
    """
    print(f"Classifying markdown files from {source_dir} to {target_dir}")

    # Ensure target directory exists
    os.makedirs(target_dir, exist_ok=True)

    # Initialize OpenAI client
    client = OpenAI()

    # Get all .json files in the source directory
    json_files = [f for f in os.listdir(source_dir) if f.endswith('.json')]

    for index, json_file in enumerate(json_files):
        json_path = os.path.join(source_dir, json_file)

        # Read the JSON file to get the MarkdownMetadata
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                metadata_dict = json.load(f)
                # Check if this is a MarkdownMetadata file
                if metadata_dict.get('step') != 'markdown':
                    print(f"Skipping {json_file} because it's not a markdown metadata file")
                    continue
                metadata = MarkdownMetadata.model_validate(metadata_dict)
        except Exception as e:
            print(f"Error reading metadata from {json_file}: {str(e)}")
            continue

        # Get the markdown file path
        markdown_file = metadata.markdown_file
        markdown_path = os.path.join(source_dir, markdown_file)

        # Check if the markdown file exists
        if not os.path.exists(markdown_path):
            print(f"Skipping {json_file} because markdown file {markdown_file} does not exist")
            continue

        # Read the markdown file
        try:
            with open(markdown_path, 'r', encoding='utf-8') as f:
                markdown_content = f.read()
        except Exception as e:
            print(f"Error reading markdown file {markdown_file}: {str(e)}")
            continue

        # Skip if the markdown content is too short
        if len(markdown_content.strip()) < 50:
            print(f"Skipping {markdown_file} because content is too short")
            continue

        # Call OpenAI API to classify the document
        try:
            response = client.beta.chat.completions.parse(
                model="gpt-4.1-mini",
                messages=[
                    {"role": "system", "content": f"{classification_system_prompt}\n\nDO NOT FILL THE chunks property!"},
                    {"role": "user", "content": markdown_content}
                ],
                temperature=0.0,  # Use deterministic output
                response_format=ClassificationResponse,
            )

            classification = response.choices[0].message.parsed
            
            # Ensure we have proper dictionaries for nested models
            if classification.organisation_knowledge:
                # Convert any OrganisationKnowledge objects to dictionaries if needed
                org_knowledge_list = []
                for org in classification.organisation_knowledge:
                    if isinstance(org, dict):
                        org_knowledge_list.append(org)
                    else:
                        org_knowledge_list.append(org.model_dump())
                classification.organisation_knowledge = org_knowledge_list
                
            if classification.people_knowledge:
                # Convert any PeopleKnowledge objects to dictionaries if needed
                people_knowledge_list = []
                for person in classification.people_knowledge:
                    if isinstance(person, dict):
                        people_knowledge_list.append(person)
                    else:
                        people_knowledge_list.append(person.model_dump())
                classification.people_knowledge = people_knowledge_list

            # Create a ClassificationMetadata object
            classification_metadata = ClassificationMetadata.create_from_markdown_metadata(
                markdown_metadata=metadata,
                date=classification.date,
                title=classification.title,
                description=classification.description,
                people_knowledge=classification.people_knowledge,
                organisation_knowledge=classification.organisation_knowledge,
            )

            # Save the ClassificationMetadata to a JSON file in the target directory
            target_json_path = os.path.join(target_dir, json_file)
            with open(target_json_path, 'w', encoding='utf-8') as f:
                json.dump(classification_metadata.model_dump(), f, indent=2)

            # Copy the markdown file to the target directory
            target_markdown_path = os.path.join(target_dir, markdown_file)
            shutil.copy2(markdown_path, target_markdown_path)

            print(f"Classified {markdown_file} {index+1}/{len(json_files)}")

        except Exception as e:
            print(f"Error classifying {markdown_file}: {str(e)}")
            continue
