"""
Models for classification.

This module contains Pydantic models used by the classification tools to represent
metadata and other structured data related to classified files.
"""

from typing import Optional, List
from pydantic import BaseModel, Field
from crawler.tools.markdown_model import MarkdownMetadata


class OrganisationKnowledge(BaseModel):
    name: str
    knowledge_sentences_about_that_organisation: list[str]

class OrganisationKnowledge(BaseModel):
    firstname: str
    lastname: str
    title: Optional[str]
    knowledge_sentences_about_that_person: list[str]

class ClassificationMetadata(MarkdownMetadata):
    """
    Metadata for a classified markdown file.

    This class extends MarkdownMetadata to include information about the classification
    of the markdown file, such as date, title, and description.
    """

    step: str = Field(
        default="classification",
        description="The processing step that created this metadata"
    )

    date: Optional[str] = Field(
        default=None,
        description="The date the document is referring to, in ISO 8601 format (YYYY-MM-DD)"
    )

    title: str = Field(
        description="A generated title for the document"
    )

    description: Optional[str] = Field(
        default=None,
        description="A 100 word long description for the document"
    )

    people_knowledge: Optional[list[OrganisationKnowledge]] = Field(
        default=None,
        description="Knowledge about people out of the document"
    )

    organisation_knowledge: Optional[list[OrganisationKnowledge]] = Field(
        default=None,
        description="Knowledge about the organisations out of the document"
    )

    markdown_chunk_files: Optional[list[str]] = Field(
        default=None,
        description="A list of markdown filenames where the original content was split up to"
    )


    @classmethod
    def create_from_markdown_metadata(
        cls,
        markdown_metadata: MarkdownMetadata,
        date: Optional[str],
        title: str,
        description: Optional[str],
        people_knowledge: Optional[list[OrganisationKnowledge]],
        organisation_knowledge: Optional[list[OrganisationKnowledge]],
        markdown_chunk_files: Optional[list[str]]
    ) -> "ClassificationMetadata":
        """
        Create a new ClassificationMetadata instance from an existing MarkdownMetadata.

        Args:
            markdown_metadata: The original MarkdownMetadata instance
            date: The date the document is referring to
            title: A generated title for the document
            description: A description for the document

        Returns:
            A new ClassificationMetadata instance
        """
        return cls(
            original_file=markdown_metadata.original_file,
            mimetype=markdown_metadata.mimetype,
            source=markdown_metadata.source,
            type=markdown_metadata.type,
            crawled_at=markdown_metadata.crawled_at,
            markdown_file=markdown_metadata.markdown_file,
            date=date,
            title=title,
            description=description,
            people_knowledge=people_knowledge,
            organisation_knowledge=organisation_knowledge,
            markdown_chunk_files=markdown_chunk_files
        )



class EmbeddingMetadata(ClassificationMetadata):
    """
    Metadata for an embedded file.

    This class extends ClassificationMetadata to include information about the embedding
    of the file, such as the list of embedding chunk files.
    """

    step: str = Field(
        default="embedding",
        description="The processing step that created this metadata"
    )

    embedding_files: List[str] = Field(
        default_factory=list,
        description="The list of files containing the embeddings for this document"
    )

    @classmethod
    def create_from_classification_metadata(cls, classification_metadata: ClassificationMetadata,
                                          embedding_files: List[str]) -> "EmbeddingMetadata":
        """
        Create a new EmbeddingMetadata instance from an existing ClassificationMetadata.

        Args:
            classification_metadata: The original ClassificationMetadata instance
            embedding_files: The list of files containing the embeddings

        Returns:
            A new EmbeddingMetadata instance
        """
        return cls(
            original_file=classification_metadata.original_file,
            mimetype=classification_metadata.mimetype,
            source=classification_metadata.source,
            type=classification_metadata.type,
            crawled_at=classification_metadata.crawled_at,
            markdown_file=classification_metadata.markdown_file,
            date=classification_metadata.date,
            title=classification_metadata.title,
            description=classification_metadata.description,
            embedding_files=embedding_files,
            people_knowledge=classification_metadata.people_knowledge,
            organisation_knowledge=classification_metadata.organisation_knowledge,
            markdown_chunk_files=classification_metadata.markdown_chunk_files
        )
