from spire.doc import *
from spire.doc.common import *

def convert_all_doc_to_docx(path: str):
    all_files = os.listdir(path)
    all_doc_files = [file for file in all_files if file.endswith(".doc") or file.endswith(".DOC")]
    for index, file in enumerate(all_doc_files):
        source_file_path = os.path.join(path, file)
        file_name = os.path.splitext(file)[0]
        target_file_path = os.path.join(path, file_name + ".docx")
        print(f"Converting doc to docx {index+1}/{len(all_doc_files)}")

        # Create an object of the Document class
        document = Document()
        # Load a Word DOC file
        document.LoadFromFile(source_file_path)

        # Save the DOC file to DOCX format
        document.SaveToFile(target_file_path, FileFormat.Docx2016)
        # Close the Document object
        document.Close()

        # Remove the original doc file
        os.remove(source_file_path) 