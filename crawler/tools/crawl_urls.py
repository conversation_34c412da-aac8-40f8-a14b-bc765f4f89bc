import requests
import os
import time
import json
from typing import Callable, Optional, Set, List
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin, urldefrag
from .crawl_urls_model import CrawlMetadata

def crawl_url(url: str, path: str, guard: Optional[Callable[[str, str, str], bool]] = None):
    print(f"Crawling {url}")

    # Ensure the path exists
    os.makedirs(path, exist_ok=True)

    # Get the resource
    response = requests.get(url)

    # Determine the filename from the URL
    # it should be the full path of the url, replacing / with -
    # also remove all special characters
    parsed_url = urlparse(url)
    sanitized_path = parsed_url.path.lstrip('/').replace('/', '-')

    filename = sanitized_path # this is the filename of the url
    if not filename:
        filename = 'index.html'

    # Determine the mimetype
    content_type = response.headers.get('Content-Type', '')
    mimetype = content_type.split(';')[0].strip()

    # If it's HTML content but doesn't have .html or .htm extension, add .html
    if mimetype == 'text/html' and not (filename.endswith('.html') or filename.endswith('.htm')):
        if '.' in filename:
            # Replace existing extension with .html
            filename = filename.rsplit('.', 1)[0] + '.html'
        else:
            # Add .html extension
            filename = filename + '.html'

    # Mimetype already determined above

    # If it's HTML content and guard function is provided, check if we should save it
    if mimetype == 'text/html' and guard is not None:
        html_content = response.text
        if not guard(url, mimetype, html_content):
            print(f"Guard rejected {url}")
            return
    # For non-HTML content with guard function
    elif guard is not None:
        if not guard(url, mimetype, ''):
            print(f"Guard rejected {url}")
            return

    # Save the resource to the path
    file_path = os.path.join(path, filename)
    with open(file_path, "wb") as f:
        f.write(response.content)

    # Determine the document type based on mimetype
    doc_type = "unknown"
    if mimetype.startswith("text/html"):
        doc_type = "html"
    elif mimetype.startswith("application/pdf"):
        doc_type = "pdf"
    elif mimetype.startswith("application/msword") or mimetype.startswith("application/vnd.openxmlformats-officedocument.wordprocessingml"):
        doc_type = "word"
    elif mimetype.startswith("application/vnd.ms-excel") or mimetype.startswith("application/vnd.openxmlformats-officedocument.spreadsheetml"):
        doc_type = "excel"
    elif mimetype.startswith("image/"):
        doc_type = "image"
    else:
        # Extract type from extension if possible
        if '.' in filename:
            ext = filename.rsplit('.', 1)[1].lower()
            if ext in ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png', 'gif']:
                doc_type = ext

    # Create metadata using the CrawlMetadata model
    metadata = CrawlMetadata.create(
        filename=filename,
        mimetype=mimetype,
        url=url,
        doc_type=doc_type
    )

    # Save metadata to JSON file
    json_path = f"{file_path}.json"
    with open(json_path, "w", encoding="utf-8") as f:
        json.dump(metadata.model_dump(), f, indent=2)

    print(f"Saved {url} to {file_path} with metadata at {json_path}")

    if mimetype == 'text/html':
        return response.text
    else:
        return None


# Tree structure to represent the crawl
class UrlNode:
    def __init__(self, url: str, depth: int):
        self.url = url
        self.depth = depth
        self.children: List['UrlNode'] = []

    def add_child(self, child: 'UrlNode') -> None:
        self.children.append(child)

def print_url_tree(root: UrlNode, indent: str = "") -> None:
    """Pretty print the URL tree structure"""
    print(f"{indent}├── {root.url} (depth: {root.depth})")
    for i, child in enumerate(root.children):
        if i == len(root.children) - 1:  # Last child
            print_url_tree(child, indent + "    ")
        else:
            print_url_tree(child, indent + "│   ")

def crawl_urls(urls: list[str], path: str):
    for url in urls:
        crawl_url(url, path)

def crawl_and_build_tree(url: str, path: str, max_depth: int = 3,
                        guard: Optional[Callable[[str, str, str], bool]] = None,
                        delay: float = 0.5) -> UrlNode:
    """Crawl a URL and build a tree structure of the crawled URLs"""
    # Reset the visited URLs set
    _visited_urls.clear()

    # Start the recursive crawl and build the tree
    tree = recursive_crawl(url, path, 0, max_depth, guard, delay)

    # Print the tree
    if tree:
        print("\nURL Tree Structure:")
        print("===================\n")
        print_url_tree(tree)

    return tree

# Set to keep track of visited URLs
_visited_urls: Set[str] = set()

def recursive_crawl(url: str, path: str, depth: int = 0, max_depth: int = 3,
                   guard: Optional[Callable[[str, str, str], bool]] = None,
                   delay: float = 0.5, visited_urls: Optional[Set[str]] = None,
                   original_domain: Optional[str] = None, parent_node: Optional[UrlNode] = None,
                   root_node: Optional[UrlNode] = None) -> Optional[UrlNode]:
    # Remove any fragments (anchors) from the URL
    url, _ = urldefrag(url)

    # Initialize or use the provided set of visited URLs
    if visited_urls is None:
        visited_urls = _visited_urls

    # Check if URL has already been visited
    if url in visited_urls:
        print(f"Skipping already visited URL: {url}")
        return None

    # Extract the domain from the URL
    parsed_url = urlparse(url)
    current_domain = parsed_url.netloc

    # Store the original domain if this is the first call
    if original_domain is None:
        original_domain = current_domain
    # Skip if the domain is different from the original domain
    elif current_domain != original_domain:
        print(f"Skipping external domain: {url}")
        return None

    # Create a node for this URL
    current_node = UrlNode(url, depth)

    # If this is the root call, store the root node
    if root_node is None:
        root_node = current_node

    # If there's a parent node, add this node as its child
    if parent_node is not None:
        parent_node.add_child(current_node)

    # Add URL to visited set before crawling
    visited_urls.add(url)

    # Crawl the URL
    html_content = crawl_url(url, path, guard)
    if html_content is None or depth >= max_depth:
        print(f'Max depth reached or no HTML content found. {url} {depth} {max_depth}')
        return root_node if parent_node is None else None

    # Use the parsed URL from above
    base_domain = f"{parsed_url.scheme}://{parsed_url.netloc}"

    soup = BeautifulSoup(html_content, 'html.parser')
    # Parse the HTML content and find all links
    # For simplicity, we'll just look for href attributes in <a> tags
    links = [a['href'] for a in soup.find_all('a', href=True)]

    for link in links:
        # Skip empty links or fragment-only links
        if not link or link.startswith("#") or link.startswith("javascript:"):
            continue

        # Try to construct a valid link
        valid_link = None

        # If it's already an absolute URL
        if link.startswith("http"):
            valid_link = link
        # If it's a relative URL starting with /
        elif link.startswith("/"):
            valid_link = base_domain + link
        # If it's a relative URL without leading /
        else:
            # Use urljoin to handle relative paths correctly
            valid_link = urljoin(url, link)

        # Verify the link has a valid structure
        parsed_link = urlparse(valid_link)
        if not parsed_link.scheme or not parsed_link.netloc:
            print(f"Skipping invalid link: {link}")
            continue

        # Remove any fragments (anchors) from the URL
        valid_link, _ = urldefrag(valid_link)

        # Check if the link has already been visited
        if valid_link in visited_urls:
            print(f"Skipping already visited URL: {valid_link}")
            continue

        # Proceed with the valid link
        # Apply delay between requests (500ms)
        time.sleep(delay)
        recursive_crawl(valid_link, path, depth + 1, max_depth, guard, delay, visited_urls,
                      original_domain, current_node, root_node)

    # Return the root node if this is the root call
    return root_node if parent_node is None else None