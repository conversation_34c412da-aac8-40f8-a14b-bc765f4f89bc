"""
Models for the crawler tools.

This module contains Pydantic models used by the crawler tools to represent
metadata and other structured data.
"""

from datetime import datetime, timezone
from typing import Any, Dict, Literal, Optional
from pydantic import BaseModel, Field


class CrawlMetadata(BaseModel):
    """
    Metadata for a crawled resource.
    
    This class represents the metadata associated with a file that has been
    crawled from a URL. It includes information about the source, file type,
    and crawling timestamp.
    """
    
    step: str = Field(
        default="crawl-html",
        description="The processing step that created this metadata"
    )
    
    original_file: str = Field(
        description="The filename of the crawled resource"
    )
    
    mimetype: str = Field(
        description="The MIME type of the crawled resource"
    )
    
    source: str = Field(
        description="The URL from which the resource was crawled"
    )
    
    type: str = Field(
        description="The document type, derived from the MIME type or file extension"
    )
    
    crawled_at: str = Field(
        default_factory=lambda: datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
        description="The timestamp when the resource was crawled, in ISO 8601 format"
    )
    
    @classmethod
    def create(cls, filename: str, mimetype: str, url: str, doc_type: str) -> "CrawlMetadata":
        """
        Create a new CrawlMetadata instance with the given parameters.
        
        Args:
            filename: The filename of the crawled resource
            mimetype: The MIME type of the crawled resource
            url: The URL from which the resource was crawled
            doc_type: The document type
            
        Returns:
            A new CrawlMetadata instance
        """
        return cls(
            original_file=filename,
            mimetype=mimetype,
            source=url,
            type=doc_type
        )
