import os
import json
import shutil
from crawler.tools import tmp_crawl_path, data_repo_path
from crawler.tools.classification_model import EmbeddingMetadata

def move_raw_crawled_data_to_data_repo(target_dir: str):
    """
    Move processed data from the temporary crawl directory to the data repository.

    This function creates a folder with the given identifier in the data repository,
    then copies all embedding metadata JSON files, embedding files, and markdown files
    from the temporary crawl directory to the data repository.

    Args:
        target_dir: the target where to move the data to
    """
    # remove the current folder
    if os.path.exists(target_dir):
        shutil.rmtree(target_dir)
    # Create the target directory in the data repository
    os.makedirs(target_dir, exist_ok=True)

    # Path to the embedding directory in the temporary crawl directory
    embedding_dir = os.path.join(tmp_crawl_path, "embedding")
    classification_dir = os.path.join(tmp_crawl_path, "classification")
    markdown_dir = os.path.join(tmp_crawl_path, "markdown")

    # Find all JSON files in the embedding directory
    json_files = [f for f in os.listdir(embedding_dir) if f.endswith(".json")]

    print(f"Moving {len(json_files)} files to {target_dir}")

    for index, json_file in enumerate(json_files):
        print(f"{index+1}/{len(json_files)} Moving {json_file}")

        # Full path to the JSON file
        json_path = os.path.join(embedding_dir, json_file)

        # Load the JSON file as EmbeddingMetadata
        with open(json_path, "r", encoding="utf-8") as f:
            metadata_dict = json.load(f)
            metadata = EmbeddingMetadata.model_validate(metadata_dict)

        # Copy the JSON file to the target directory
        target_json_path = os.path.join(target_dir, json_file)
        shutil.copy2(json_path, target_json_path)

        # Copy all embedding files to the target directory
        for embedding_file in metadata.embedding_files:
            embedding_file_path = os.path.join(embedding_dir, embedding_file)
            target_embedding_path = os.path.join(target_dir, embedding_file)
            shutil.copy2(embedding_file_path, target_embedding_path)

        # Copy all embedding files to the target directory
        if metadata.markdown_chunk_files:
            for markdown_chunk_file in metadata.markdown_chunk_files:
                markdown_chunk_file_path = os.path.join(classification_dir, markdown_chunk_file)
                target_chunk_path = os.path.join(target_dir, markdown_chunk_file)
                shutil.copy2(markdown_chunk_file_path, target_chunk_path)

        # Copy the markdown file to the target directory
        if metadata.markdown_file:
            markdown_file_path = os.path.join(markdown_dir, metadata.markdown_file)
            target_markdown_path = os.path.join(target_dir, metadata.markdown_file)
            shutil.copy2(markdown_file_path, target_markdown_path)

    print(f"Successfully moved all files to {target_dir}")