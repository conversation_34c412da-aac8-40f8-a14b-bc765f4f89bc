import os
import json
import tiktoken
from typing import List
from openai import OpenAI
from mistralai import Mistral
from crawler.tools.classification_model import ClassificationMetadata, EmbeddingMetadata


# Initialize OpenAI client
client = OpenAI()

# Initialize tokenizer
encoding = tiktoken.get_encoding("cl100k_base")


def embed_documents_after_markdown_step(source_dir: str, target_dir: str):
    """
    Embed markdown files from a previous classification step and store them in the target directory.

    This function looks for .json files in the source_dir that contain ClassificationMetadata.
    It reads the markdown file, embeds it, and stores the result in the target_dir with a new
    EmbeddingMetadata file.

    Args:
        source_dir: Directory containing the .json metadata files and markdown files
        target_dir: Directory where the embedded files and new metadata will be stored
    """
    print(f"Embedding markdown files from {source_dir} to {target_dir}")

    # Ensure target directory exists
    os.makedirs(target_dir, exist_ok=True)

    # Get all .json files in the source directory
    json_files = [f for f in os.listdir(source_dir) if f.endswith('.json')]

    for index, json_file in enumerate(json_files):
        json_path = os.path.join(source_dir, json_file)

        # Read the JSON file to get the ClassificationMetadata
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                metadata_dict = json.load(f)
                # Check if this is a ClassificationMetadata file
                if metadata_dict.get('step') != 'classification':
                    print(f"Skipping {json_file} because it's not a classification metadata file")
                    continue
                metadata = ClassificationMetadata.model_validate(metadata_dict)
        except Exception as e:
            print(f"Error reading metadata from {json_file}: {str(e)}")
            continue

        # List to store the names of the embedding files
        embedding_files = []

        # Check if there are markdown chunk files
        if metadata.markdown_chunk_files and len(metadata.markdown_chunk_files) > 0:
            # Process each chunk file
            print(f"Processing {len(metadata.markdown_chunk_files)} markdown chunk files")

            for i, chunk_file in enumerate(metadata.markdown_chunk_files):
                chunk_path = os.path.join(source_dir, chunk_file)

                # Check if the chunk file exists
                if not os.path.exists(chunk_path):
                    print(f"Skipping chunk {chunk_file} because it does not exist")
                    continue

                # Read the chunk file
                try:
                    with open(chunk_path, 'r', encoding='utf-8') as f:
                        chunk_content = f.read()
                except Exception as e:
                    print(f"Error reading chunk file {chunk_file}: {str(e)}")
                    continue

                # Skip if the chunk content is too short
                if len(chunk_content.strip()) < 50:
                    print(f"Skipping {chunk_file} because content is too short")
                    continue

                # Embed the chunk using the extracted function
                print(f"Embedding chunk {i+1}/{len(metadata.markdown_chunk_files)}: {chunk_file}")
                base_chunk_filename = os.path.splitext(chunk_file)[0]
                chunk_embedding_files = embed_content_with_metadata(
                    content=chunk_content,
                    metadata=metadata,
                    base_filename=base_chunk_filename,
                    target_dir=target_dir
                )

                # Add all embedding files from this chunk to the main list
                embedding_files.extend(chunk_embedding_files)

        else:
            # No chunks, process the main markdown file
            markdown_file = metadata.markdown_file
            markdown_path = os.path.join(source_dir, markdown_file)

            # Check if the markdown file exists
            if not os.path.exists(markdown_path):
                print(f"Skipping {json_file} because markdown file {markdown_file} does not exist")
                continue

            # Read the markdown file
            try:
                with open(markdown_path, 'r', encoding='utf-8') as f:
                    markdown_content = f.read()
            except Exception as e:
                print(f"Error reading markdown file {markdown_file}: {str(e)}")
                continue

            # Skip if the markdown content is too short
            if len(markdown_content.strip()) < 50:
                print(f"Skipping {markdown_file} because content is too short")
                continue

            # Embed the markdown file using the extracted function
            print(f"Processing markdown file: {markdown_file}")
            base_filename = os.path.splitext(markdown_file)[0]
            embedding_files = embed_content_with_metadata(
                content=markdown_content,
                metadata=metadata,
                base_filename=base_filename,
                target_dir=target_dir
            )

        # Create an EmbeddingMetadata object
        embedding_metadata = EmbeddingMetadata.create_from_classification_metadata(
            classification_metadata=metadata,
            embedding_files=embedding_files
        )

        # Save the EmbeddingMetadata to a JSON file in the target directory
        target_json_path = os.path.join(target_dir, json_file)
        with open(target_json_path, 'w', encoding='utf-8') as f:
            json.dump(embedding_metadata.model_dump(), f, indent=2)

        print(f"Embedded {json_file} {index+1}/{len(json_files)}")


def embed_content_with_metadata(content: str, metadata: ClassificationMetadata,
                               base_filename: str, target_dir: str) -> List[str]:
    """
    Embed content with metadata template and handle chunking if necessary.

    Args:
        content: The markdown content to embed
        metadata: The classification metadata containing title, date, description
        base_filename: Base filename for the embedding files (without extension)
        target_dir: Directory where embedding files will be saved

    Returns:
        List of embedding filenames that were created
    """
    # Prepare the template for embedding
    template = f"""{metadata.title or ''}
{metadata.date or ''}
{metadata.description or ''}
---
{content}"""

    # Check if the document is longer than 8000 tokens
    tokens = encoding.encode(template)
    token_count = len(tokens)

    # List to store the names of the embedding files
    embedding_files = []

    if token_count > 8000:
        # Split the document into chunks
        print(f"Splitting content into chunks (token count: {token_count})")

        # Calculate the number of chunks needed
        chunk_size = 8000
        num_chunks = (token_count + chunk_size - 1) // chunk_size  # Ceiling division

        for i in range(num_chunks):
            # Get the chunk of tokens
            start_idx = i * chunk_size
            end_idx = min((i + 1) * chunk_size, token_count)
            chunk_tokens = tokens[start_idx:end_idx]

            # Decode the chunk back to text
            chunk_text = encoding.decode(chunk_tokens)

            # Embed the chunk
            embedding = embed_document(chunk_text)

            # Save the embedding to a file
            chunk_filename = f"{base_filename}-chunk-{i}.txt"
            chunk_path = os.path.join(target_dir, chunk_filename)

            with open(chunk_path, 'w', encoding='utf-8') as f:
                f.write(str(embedding))

            embedding_files.append(chunk_filename)
            print(f"Saved chunk {i+1}/{num_chunks} to {chunk_filename}")
    else:
        # Embed the entire document
        print(f"Embedding content (token count: {token_count})")
        embedding = embed_document(template)

        # Save the embedding to a file
        embedding_filename = f"{base_filename}-embedding.txt"
        embedding_path = os.path.join(target_dir, embedding_filename)

        with open(embedding_path, 'w', encoding='utf-8') as f:
            f.write(str(embedding))

        embedding_files.append(embedding_filename)
        print(f"Saved embedding to {embedding_filename}")

    return embedding_files


def embed_document(text: str) -> List[float]:
    """
    Embed a document with the OpenAI API or Mistral API,
    returns the vector.

    Args:
        text: The text to embed

    Returns:
        The embedding vector
    """
    try:
        response = client.embeddings.create(
            model="text-embedding-3-small",
            input=text
        )
        return response.data[0].embedding
    except Exception as e:
        print(f"Error embedding document: {str(e)}")
        # Return an empty list as a fallback
        return []