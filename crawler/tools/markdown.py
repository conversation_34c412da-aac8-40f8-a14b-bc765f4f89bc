import os
import json
from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.output import text_from_rendered
from marker.config.parser import ConfigParser
from crawler.tools.crawl_urls_model import CrawlMetadata
from crawler.tools.markdown_model import MarkdownMetadata

config_parser = None
converter = None

# Initialize Marker
marker_config = {
    "output_format": "markdown",
    "disable_image_extraction": True,
    # "use_llm": True,
    # "gemini_api_key": os.environ.get("GOOGLE_API_KEY")
}


def convert_files_to_markdown_after_crawl_step(source_dir: str, target_dir: str):
    """
    Convert files from a previous crawl step to markdown and store them in the target directory.

    This function looks for .json files in the source_dir that contain CrawlMetadata.
    It takes the original_file from the metadata, converts it to markdown, and stores
    the result in the target_dir. It also creates a new MarkdownMetadata file.

    Args:
        source_dir: Directory containing the .json metadata files and original files
        target_dir: Directory where the markdown files and new metadata will be stored
    """
    print("Converting files to markdown after crawl step")

    # Ensure target directory exists
    os.makedirs(target_dir, exist_ok=True)

    # Get all .json files in the source directory
    json_files = [f for f in os.listdir(source_dir) if f.endswith('.json')]

    for json_file in json_files:
        json_path = os.path.join(source_dir, json_file)

        # Read the JSON file to get the CrawlMetadata
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                metadata_dict = json.load(f)
                metadata = CrawlMetadata.model_validate(metadata_dict)
        except Exception as e:
            print(f"Error reading metadata from {json_file}: {str(e)}")
            continue

        # Skip if the step is already "markdown"
        if metadata.step == "markdown":
            print(f"Skipping {json_file} because it's already processed as markdown")
            continue

        # Get the original file path
        original_file = metadata.original_file
        original_file_path = os.path.join(source_dir, original_file)

        # Check if the original file is a .doc file and look for a .docx version
        file_to_use = original_file
        file_path_to_use = original_file_path

        if original_file.lower().endswith('.doc'):
            # Try to find a .docx version of the file
            docx_file = original_file[:-4] + '.docx'  # Replace .doc with .docx
            docx_file_path = os.path.join(source_dir, docx_file)

            if os.path.exists(docx_file_path):
                print(f"Found .docx version for {original_file}, using {docx_file} instead")
                file_to_use = docx_file
                file_path_to_use = docx_file_path
            else:
                print(f"Skipping {json_file} because no .docx version found for {original_file}")
                continue
        # Skip if the file to use doesn't exist
        elif not os.path.exists(file_path_to_use):
            print(f"Skipping {json_file} because file {file_to_use} doesn't exist")
            continue

        # Create the markdown filename - still based on the original file name for consistency
        markdown_filename = f"{os.path.splitext(original_file)[0]}.md"
        markdown_file_path = os.path.join(target_dir, markdown_filename)

        # Convert the file to markdown
        try:
            md_text = document_to_md(file_path_to_use)

            # Skip if the markdown content is less than 50 characters
            if len(md_text.strip()) < 50:
                print(f"Skipping {file_to_use} because markdown content is too short")
                continue

            # Save the markdown file
            with open(markdown_file_path, 'w', encoding='utf-8') as f:
                f.write(md_text)

            # Create a new MarkdownMetadata object
            # Create a copy of the metadata to modify
            updated_metadata = CrawlMetadata(
                step=metadata.step,
                original_file=file_to_use,  # Use the file we actually converted
                mimetype=metadata.mimetype,
                source=metadata.source,
                type=metadata.type,
                crawled_at=metadata.crawled_at
            )

            markdown_metadata = MarkdownMetadata.create_from_crawl_metadata(
                crawl_metadata=updated_metadata,
                markdown_filename=markdown_filename
            )

            # Save the new metadata to a JSON file
            markdown_metadata_path = os.path.join(target_dir, f"{markdown_filename}.json")
            with open(markdown_metadata_path, 'w', encoding='utf-8') as f:
                json.dump(markdown_metadata.model_dump(), f, indent=2)

            print(f"Converted {file_to_use} to {markdown_filename}")

        except Exception as e:
            print(f"Error converting {file_to_use} to markdown: {str(e)}")
            continue

# Convert any document to markdown using marker
def document_to_md(file_path: str) -> str:
    global config_parser, converter

    if config_parser is None:
        config_parser = ConfigParser(marker_config)
    if converter is None:
        converter = PdfConverter(
            config=config_parser.generate_config_dict(),
            artifact_dict=create_model_dict(),
            llm_service=config_parser.get_llm_service()
        )

    rendered = converter(file_path)
    text, _, _ = text_from_rendered(rendered)
    return text