"""
Models for markdown conversion.

This module contains Pydantic models used by the markdown conversion tools to represent
metadata and other structured data related to markdown files.
"""

from pydantic import Field
from crawler.tools.crawl_urls_model import CrawlMetadata


class MarkdownMetadata(CrawlMetadata):
    """
    Metadata for a markdown file converted from a crawled resource.
    
    This class extends CrawlMetadata to include information about the markdown file
    that was created from the original crawled resource.
    """
    
    step: str = Field(
        default="markdown",
        description="The processing step that created this metadata"
    )
    
    markdown_file: str = Field(
        description="The filename of the markdown file created from the original resource"
    )
    
    @classmethod
    def create_from_crawl_metadata(cls, crawl_metadata: CrawlMetadata, markdown_filename: str) -> "MarkdownMetadata":
        """
        Create a new MarkdownMetadata instance from an existing CrawlMetadata.
        
        Args:
            crawl_metadata: The original CrawlMetadata instance
            markdown_filename: The filename of the markdown file
            
        Returns:
            A new MarkdownMetadata instance
        """
        return cls(
            original_file=crawl_metadata.original_file,
            mimetype=crawl_metadata.mimetype,
            source=crawl_metadata.source,
            type=crawl_metadata.type,
            crawled_at=crawl_metadata.crawled_at,
            markdown_file=markdown_filename
        )
