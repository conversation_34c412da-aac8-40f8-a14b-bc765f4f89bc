import os
import glob
from typing import List
from bs4 import BeautifulSoup

def sanitize_html(path: str, content_containers: List[str], html_blacklist: List[str]) -> None:
    """Sanitize HTML files by keeping only the content container and removing blacklisted elements.

    Args:
        path: The folder where to search for HTML files
        content_containers: List of CSS selectors for the main content container (tries each until one is found)
        html_blacklist: List of CSS selectors to remove from the HTML
    """
    # Find all HTML files in the specified path
    html_files = glob.glob(os.path.join(path, "**/*.html"), recursive=True)
    html_files.extend(glob.glob(os.path.join(path, "**/*.htm"), recursive=True))

    print(f"Found {len(html_files)} HTML files to sanitize")

    for html_file in html_files:
        try:
            print(f"Sanitizing {html_file}")

            # Read the HTML file
            with open(html_file, "r", encoding="utf-8", errors="replace") as f:
                html_content = f.read()

            # Parse the HTML with BeautifulSoup
            soup = BeautifulSoup(html_content, "html.parser")

            # Try each content container selector until one is found
            content = None

            for container_selector in content_containers:
                content = soup.select_one(container_selector)
                if content:
                    print(f"  Found content using selector: '{container_selector}'")
                    break

            if not content:
                print(f"  Warning: No content container found in {html_file} - removing file")
                os.remove(html_file)
                continue

            # Create a new soup with just the content
            new_soup = BeautifulSoup("", "html.parser")
            new_soup.append(content)

            # Remove blacklisted elements
            for selector in html_blacklist:
                for element in new_soup.select(selector):
                    print(f"  Removing element: {selector}")
                    element.decompose()

            # Remove empty tags
            for tag in new_soup.find_all():
                if not tag.text.strip():
                    tag.decompose()

            # Write the sanitized HTML back to the file
            with open(html_file, "w", encoding="utf-8") as f:
                f.write(str(new_soup))

            print(f"  Sanitized {html_file}")

        except Exception as e:
            print(f"  Error sanitizing {html_file}: {str(e)}")