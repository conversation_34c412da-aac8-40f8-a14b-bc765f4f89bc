from enum import Enum
from typing import Optional
from pydantic import BaseModel


metadata_system_propmt = """
You receive the protocol of an event and you have to extract the metadata from it.
Here is a description of the properties you have to extract:
- attending_people: List of people that were present at the described event or document
- missing_people: List of people that were listed as not present at the described event or document
- attending_organizations: List of organizations that were present at the described event or document
- topics: List of topics that are part of the event or document
- location: Location where the event took place or location that is describes in the document
- type_of_event: Type of event that is described in the document. The type of event can be one of the following: Übung, Einsatz, Fest, Gemeinderatssitzung

People that are missing should not be included in the attending_people list.
Remove titles like Bgm., Vzbgm., GR, Ing. Ma. etc. from all names.
Names of people should not include any titles and be store as [Lastname] [Firstname].
Do everything in german. 
"""

class EventType(Enum):
    UEBUNG = "Übung"
    EINSATZ = "Einsatz"
    FEST = "Fest"
    GEMEINDERATSITZUNG = "Gemeinderatssitzung"

class Metadata(BaseModel):
    attending_people: Optional[list[str]]
    missing_people: Optional[list[str]]
    attending_organizations: Optional[list[str]]
    topics: Optional[list[str]]
    type_of_event: Optional[EventType]