import os

import requests

from data.tools.add_source import add_source, Config
from bs4 import BeautifulSoup

def scrape_ff_man(config: Config):
    path = os.path.join(os.path.dirname(__file__), "tmp/1_scrape_html")
    os.makedirs(path, exist_ok=True)

    # get the content from https://ff-mannersdorf.co.at/2025/
    aktuelles_content = requests.get("https://ff-mannersdorf.co.at/2025/").text
    while aktuelles_content is not None:
        aktuelles_soup = BeautifulSoup(aktuelles_content, "html.parser")

        # get all posts with article.post
        posts = aktuelles_soup.select("article.post")
        for post in posts:
            # get the link to the post
            link = post.select_one("a").attrs["href"]
            # get the content of the post
            post_content = requests.get(link).text
            soup = BeautifulSoup(post_content, "html.parser")
            # save the content to a file
            # file path is the full url path with - instead of /
            file_path = link.replace("https://ff-mannersdorf.co.at/", "").replace("/", "-")

            nav = soup.find(id="nav-single")
            if nav is not None:
                nav.decompose()

            slideshow = soup.select_one(".slideshow_container")
            if slideshow is not None:
                slideshow.decompose()

            bwg = soup.select_one(".bwg_container")
            if bwg is not None:
                bwg.decompose()

            bwg_style = soup.select_one("#bwg-style-0")
            if bwg_style is not None:
                bwg_style.decompose()

            share = soup.select_one(".really_simple_share")
            if share is not None:
                share.decompose()

            meta = soup.select_one(".entry-meta")
            if meta is not None:
                meta.decompose()

            footer = soup.select_one("footer")
            if footer is not None:
                footer.decompose()

            scripts = soup.select("script")
            if scripts is not None:
                for script in scripts:
                    script.decompose()

            comments = soup.select_one("#comments")
            if comments is not None:
                comments.decompose()

            with open(os.path.join(path, f"{file_path}.html"), "w") as f:
                f.write(soup.select_one('#content').prettify())

        # check if there's a link div.nav-previous > a
        next_link = aktuelles_soup.select_one("div.nav-previous > a")
        if next_link is not None:
            aktuelles_content = requests.get(next_link.attrs['href']).text

add_source({
    "source_name": "Webseite der Freiwilligen Feuerwehr Mannersdorf",
    "source_directory": "ff_mannersdorf_webseite",
    "website": "https://ff-mannersdorf.co.at/",
    "crawling_function": scrape_ff_man
})
