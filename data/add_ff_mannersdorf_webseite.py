import os

import requests

from data.tools.add_source import add_source, Config
from bs4 import BeautifulSoup

def scrape_ff_man(config: Config):
    path = os.path.join(os.path.dirname(__file__), "tmp/1_scrape_html")
    os.makedirs(path, exist_ok=True)

    index_content = requests.get(config['website']).text
    index_soup = BeautifulSoup(index_content, "html.parser")
    navigation_links = index_soup.select("ul#menu-hauptmenue a")

    for link in navigation_links:
        # get the link to the post
        link = link.attrs["href"]
        # if the link contains 'category', skip it
        if "category" in link:
            continue
        if "links" in link:
            continue
        # get the content of the post
        post_content = requests.get(link).text
        post_soup = BeautifulSoup(post_content, "html.parser")
        # save the content to a file
        # file path is the full url path with - instead of /
        file_path = link.replace("https://ff-mannersdorf.co.at/", "").replace("/", "-")
        content = post_soup.select_one('#content')

        with open(os.path.join(path, f"{file_path}.html"), "w") as f:
            f.write(content.prettify())

add_source({
    "source_name": "Webseite der Freiwilligen Feuerwehr Mannersdorf",
    "source_directory": "ff_mannersdorf_webseite",
    "website": "https://ff-mannersdorf.co.at/",
    "crawling_function": scrape_ff_man
})
