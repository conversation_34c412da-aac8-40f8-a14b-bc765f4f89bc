import os

import requests

from data.tools.add_source import add_source, Config
from bs4 import BeautifulSoup

def scrape_ff_man(config: Config):
    path = os.path.join(os.path.dirname(__file__), "tmp/1_scrape_html")
    os.makedirs(path, exist_ok=True)

    print('Scrape allgmein')
    scrape_category("https://www.ff-matzleinsdorf.at/category/aktuelles/allgemein/", path)
    print('Scrape einsaetze')
    scrape_category("https://www.ff-matzleinsdorf.at/category/aktuelles/einsaetze/", path)
    print('Scrape uebung')
    scrape_category("https://www.ff-matzleinsdorf.at/category/aktuelles/uebung/", path)

def scrape_category(url: str, path: str):
    aktuelles_content = requests.get(url).text
    while aktuelles_content is not None:
        aktuelles_soup = BeautifulSoup(aktuelles_content, "html.parser")

        # get all posts with article.post
        posts = aktuelles_soup.select("article.uk-article")
        for post in posts:
            # get the link to the post
            link = post.select_one("a").attrs["href"]
            print(f"Scrape page: {link}")
            # get the content of the post
            post_content = requests.get(link).text
            soup = BeautifulSoup(post_content, "html.parser")
            # save the content to a file
            # file path is the full url path with - instead of /
            file_path = link.replace("https://www.ff-matzleinsdorf.at/", "").replace("/", "-")

            content = soup.select_one('main.tm-content')

            galleries = content.select(".gallery")
            if galleries is not None:
                for gallery in galleries:
                    gallery.decompose()
            
            # remove all <script> tags
            for script in content(["script"]):
                script.decompose()
            # remove all <style> tags
            for style in content(["style"]):
                style.decompose()
            
            time_tag = content.select_one("time")
            if time_tag is not None:
                time = time_tag.attrs["datetime"]
                # add the time in a h2 tag to the start of the page
                time_tag = soup.new_tag("h2")
                time_tag.string = time
                content.insert(0, time_tag)

            # remove all .uk-panel divs
            for panel in soup.select(".uk-panel"):
                panel.decompose()

            with open(os.path.join(path, f"{file_path}.html"), "w") as f:
                f.write(content.prettify())

        # check if there's a link div.nav-previous > a
        pagination_links = aktuelles_soup.select(".uk-pagination > li")
        last_link = pagination_links[-1]
        last_link_class = last_link.get('class')
        if last_link_class is None:
            aktuelles_content = requests.get(last_link.select_one('a').attrs['href']).text
        elif 'uk-active' not in last_link_class:
            aktuelles_content = requests.get(last_link.select_one('a').attrs['href']).text
        else:
            aktuelles_content = None

add_source({
    "source_name": "Webseite der Freiwilligen Feuerwehr Matzleinsdorf",
    "source_directory": "ff_matzleinsdorf_webseite",
    "website": "https://www.ff-matzleinsdorf.at/",
    "crawling_function": scrape_ff_man
})