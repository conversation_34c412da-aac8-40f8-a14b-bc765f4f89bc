import os

import requests

from data.tools.add_source import add_source, Config
from bs4 import BeautifulSoup

def scrape_ff_man(config: Config):
    path = os.path.join(os.path.dirname(__file__), "tmp/1_scrape_html")
    os.makedirs(path, exist_ok=True)

    index_content = requests.get(config['website']).text
    index_soup = BeautifulSoup(index_content, "html.parser")
    navigation_links = index_soup.select("ul.uk-navbar-nav a")

    for link in navigation_links:
        # get the link to the post
        link = link.attrs["href"]
        # if the link contains 'category', skip it
        if "category" in link:
            continue
        if "links" in link:
            continue
        if link == "#":
            continue
        if link == "https://ff-matzleinsdorf.at":
            continue
        # get the content of the post
        print(f"scrape {link}")
        post_content = requests.get(link).text
        post_soup = BeautifulSoup(post_content, "html.parser")
        # save the content to a file
        # file path is the full url path with - instead of /
        file_path = link.replace("https://www.ff-matzleinsdorf.at/", "").replace("/", "-")
        content = post_soup.select_one('main.tm-content')

        galleries = content.select(".gallery")
        if galleries is not None:
            for gallery in galleries:
                gallery.decompose()

        sublinks = content.select('a')
        for sublink in sublinks:
            link_href = sublink.attrs['href']
            if link_href.startswith(config["website"]) == False:
                continue
            sublink_path = link_href.replace("https://www.ff-matzleinsdorf.at/", "").replace("/", "-")
            print(f"scrape {link_href}")
            scrape_page(link_href, path, f"{file_path}-{sublink_path}")


        with open(os.path.join(path, f"{file_path}.html"), "w") as f:
            f.write(content.prettify())

def scrape_page(url: str, path: str, identifier: str):
    overview_page = requests.get(url)
    overview_soup = BeautifulSoup(overview_page.text, "html.parser")
    overview_content = overview_soup.select_one("main.tm-content")

    if overview_content is None:
        return

    galleries = overview_content.select(".gallery")
    if galleries is not None:
        for gallery in galleries:
            gallery.decompose()

    with open(os.path.join(path, f"{identifier}.html"), "w") as f:
        f.write(overview_content.prettify())

add_source({
    "source_name": "Webseite der Freiwilligen Feuerwehr Matzleinsdorf",
    "source_directory": "ff_matzleinsdorf_webseite",
    "website": "https://www.ff-matzleinsdorf.at/",
    "crawling_function": scrape_ff_man
})
