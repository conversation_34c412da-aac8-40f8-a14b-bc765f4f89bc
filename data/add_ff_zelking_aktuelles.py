import os

import requests

from data.tools.add_source import add_source, Config
from bs4 import BeautifulSoup

def scrape_ff_man(config: Config):
    path = os.path.join(os.path.dirname(__file__), "tmp/1_scrape_html")
    os.makedirs(path, exist_ok=True)

    # get the content from https://www.ffzelking.com/eins%C3%A4tze/eins%C3%A4tze-2025/
    scrape_submenu("https://www.ffzelking.com/eins%C3%A4tze/", path, "einsaetze")
    scrape_submenu("https://www.ffzelking.com/%C3%BCbungen/", path, "uebungen")
    scrape_submenu("https://www.ffzelking.com/termine/", path, "termine")


def scrape_submenu(url: str, path: str, identifier: str):
    overview_page = requests.get(url)
    overview_soup = BeautifulSoup(overview_page.text, "html.parser")
    overview_navigation = overview_soup.select_one("#mainNav2")
    links = overview_navigation.select("a")

    for link in links:
        link_href = link.attrs["href"]
        page = requests.get(f"https://www.ffzelking.com{link_href}")
        soup = BeautifulSoup(page.text, "html.parser")
        modules = soup.select_one('#content').select('.j-module')

        for i, module in enumerate(modules):
            if(link_href[-1] == "/"):
                module_year = link_href[-5:-1]
            else:
                module_year = link_href[-4]
            
            with open(os.path.join(path, f"{module_year}-{identifier}-{i}.html"), "w") as f:
                f.write(module.prettify())

add_source({
    "source_name": "Webseite der Freiwilligen Feuerwehr Zelking",
    "source_directory": "ff_zelking_webseite",
    "website": "https://www.ffzelking.com/",
    "crawling_function": scrape_ff_man
})
