import os

import requests

from data.tools.add_source import add_source, Config
from bs4 import BeautifulSoup

def scrape_ff_man(config: Config):
    path = os.path.join(os.path.dirname(__file__), "tmp/1_scrape_html")
    os.makedirs(path, exist_ok=True)

    scrape_submenu("https://www.ffzelking.com/mitglieder/", path, "mitglieder")
    scrape_submenu("https://www.ffzelking.com/fahrzeuge/", path, "fahrzeuge")
    scrape_page("https://www.ffzelking.com/jugend/", path, "jugend")
    scrape_page("https://www.ffzelking.com/chronik/", path, "geschichte")
    scrape_page("https://www.ffzelking.com/kontakt/", path, "kontakt")

def scrape_page(url: str, path: str, identifier: str):
    overview_page = requests.get(url)
    overview_soup = BeautifulSoup(overview_page.text, "html.parser")
    overview_content = overview_soup.select_one("#content")

    with open(os.path.join(path, f"{identifier}.html"), "w") as f:
        f.write(overview_content.prettify())

def scrape_submenu(url: str, path: str, identifier: str):
    overview_page = requests.get(url)
    overview_soup = BeautifulSoup(overview_page.text, "html.parser")
    overview_navigation = overview_soup.select_one("#mainNav2")
    links = overview_navigation.select("a")

    for link in links:
        link_href = link.attrs["href"]
        link_text = link.text
        scrape_page(f"https://www.ffzelking.com{link_href}", path, f"{identifier}-{link_text}")

add_source({
    "source_name": "Webseite der Freiwilligen Feuerwehr Zelking",
    "source_directory": "ff_zelking_webseite",
    "website": "https://www.ffzelking.com/",
    "crawling_function": scrape_ff_man
})
