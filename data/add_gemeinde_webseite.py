import os

import requests
from data.tools.add_source import add_source, Config
from data.tools.add_source.scrape_website import scrape_website
from bs4 import BeautifulSoup

def crawl_gemeinde_webseite(config: Config):
    # Skip scraping if the tmp/1_scrape_html folder exists
    if os.path.exists(os.path.join(os.path.dirname(__file__), "tmp/1_scrape_html")) is False:
        scrape_website(config)
    
    # crawl aktuelles again and split the news into a separate file
    # get the content from http://www.zelking-matzleinsdorf.gv.at/aktuell.html
    url = "http://www.zelking-matzleinsdorf.gv.at/aktuell.html"
    aktuelles_content = requests.get(url).text
    # split the content of #column2 by the h2 tags and save the content between the h2 tags as a separate file
    # save all the blocks to the tmp/1_scrape_html folder
    blocks = split_by_h2(aktuelles_content)
    for i, block in enumerate(blocks):
        with open(os.path.join(os.path.dirname(__file__), "tmp/1_scrape_html", f"aktuelles_{i}.html"), "w") as f:
            f.write(block)

    # crawl wirtschaft again and split the news into a separate file

    # Cycle through all the html files in the tmp/1_scrape_html folder
    for file in os.listdir(os.path.join(os.path.dirname(__file__), "tmp/1_scrape_html")):
        if file.endswith(".html"):
            # print(f"Sanitize html file {file}")
            # read the html file
            with open(os.path.join(os.path.dirname(__file__), "tmp/1_scrape_html", file), "r") as f:
                html_content = f.read()

                # delete all files that have less than 100 characters
                soup = BeautifulSoup(html_content, "html.parser")
                text = soup.get_text().strip().replace(" ", "").replace("\n", "")
                # if there is not text, delete 
                if len(text) < 100:
                    os.remove(os.path.join(os.path.dirname(__file__), "tmp/1_scrape_html", file))
                    continue

                # overwrite the html file with the new html content
                with open(os.path.join(os.path.dirname(__file__), "tmp/1_scrape_html", file), "w") as f:
                    f.write(sanitize_html(html_content))

def split_by_h2(html_content: str):
    current_headline = None
    current_block = []
    blocks = []

    soup = BeautifulSoup(html_content, "html.parser")
    parent_div = soup.find(id="column2")  # Get the main container

    for tag in parent_div.find_all():
        if tag.name != "h2":
            tag.unwrap()  # Removes the tag but keeps its content
    
    for tag in parent_div.children:  # Iterate through h2 and p tags
        if tag.name == "h2":
            if current_block:  # Save previous block if not empty
                blocks.append(f"<html><body><h1>{current_headline}</h1>{'<br/>'.join(current_block)}</body></html>")
                current_block = []  # Reset for new section
            current_headline = tag.text
        else:
            current_block.append(tag.text)  # Collect paragraphs
    
    return blocks

def sanitize_html(html_content: str):
    # Parse the html file with a beautifulsoup parser
    soup = BeautifulSoup(html_content, "html.parser")

    # remove the div with the id column1
    logo = soup.find(id="logo")
    if logo is not None:
        logo.decompose()
    links = soup.find(id="links")
    if links is not None:
        links.decompose()
    menu = soup.find(id="menu")
    if menu is not None:
        menu.decompose()
    column1 = soup.find(id="column1")
    if column1 is not None:
        column1.decompose()
    footer = soup.find(id="footer")
    if footer is not None:
        footer.decompose()
    return soup.prettify()

add_source({
    "source_name": "Webseite der Gemeinde Zelking-Matzleinsdorf",
    "source_directory": "gemeinde_webseite",
    "website": "http://www.zelking-matzleinsdorf.gv.at/",
    "crawling_function": crawl_gemeinde_webseite,
    "crawling_force": True,
})
