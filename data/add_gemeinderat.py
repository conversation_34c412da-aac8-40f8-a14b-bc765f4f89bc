import os

from pydantic import BaseModel
import requests

from data.tools.add_source import add_source, Config
from bs4 import BeautifulSoup

from openai import OpenAI
client = OpenAI()

def scrape_ff_man(config: Config):
    path = os.path.join(os.path.dirname(__file__), "tmp/1_scrape_html")
    os.makedirs(path, exist_ok=True)

    index_content = requests.get(config['website']).text
    index_soup = BeautifulSoup(index_content, "html.parser")
    all_links = index_soup.select("#column2 a")

    file_endings = ['.doc', '.docx', '.pdf']

    for link in all_links:
        href = link.attrs["href"]
        if any(href.lower().endswith(ending) for ending in file_endings) is False:
            continue
        # download the file that is linked in href
        file_name = href.split("/")[-1]
        file_name = file_name.replace(" ", "_")
        file_name = file_name.replace("ä", "ae")
        file_name = file_name.replace("ö", "oe")
        file_name = file_name.replace("ü", "ue")
        file_name = file_name.replace("ß", "ss")

        # request the web link in href, then save the content to the file_name in the tmp directory 1_scrape_html
        file_path = os.path.join(path, file_name)
        if os.path.exists(file_path):
            print(f"File {file_name} already exists, skipping")
            continue
        print(f"Downloading {file_name}...")
        # download the file
        url = f"http://www.zelking-matzleinsdorf.gv.at/{href}"
        r = requests.get(url, allow_redirects=True)
        # save the file
        open(file_path, "wb").write(r.content)

        with open(file_path, "wb") as f:
            f.write(r.content)

class SplitProtocoll(BaseModel):
    title: str
    date: str
    chunks: list[str]

def split_protokoll():
    # loop through all files in the tmp directory 2_marker
    path = os.path.join(os.path.dirname(__file__), "tmp/2_marker")
    for file_name in os.listdir(path):
        if(file_name.endswith(".md") is False):
            continue

        get_chunck_file_name = lambda x: f"{file_name}_chunk_{x}.txt"
        first_chunk_file_name = get_chunck_file_name(0)
        # check if the file already exists
        if os.path.exists(os.path.join(path, first_chunk_file_name)):
            print(f"File {file_name} was already split, skipping")
            continue

        # open the file
        with open(os.path.join(path, file_name), "r") as f:
            print(file_name)
            content = f.read()

            completion = client.beta.chat.completions.parse(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": """
                     Du bist ein Assistent der Protokolle von Gemeinderatssitzungen aufbereitet.
                     Alle Protokolle sind in Markdown geschrieben.
                     Deine Aufgabe ist es, die Protokolle in Abschnitte anhand der Tagesordnung zu unterteilen.
                     Das Intro vor der Tagesordnung soll der erste Abschnitt sein.
                     Extrahiere auch das Datum der Sitzung in ISO Format (YYYY-MM-DD).
                     """},
                    {"role": "user", "content": content},
                ],
                temperature=0,
                response_format=SplitProtocoll,
            )

            event: SplitProtocoll = completion.choices[0].message.parsed

            # save the chunks to the tmp directory 2_marker
            for i, chunk in enumerate(event.chunks):
                with open(os.path.join(path, get_chunck_file_name(i)), "w") as f:
                    f.write(f"# Gemeinderatsitzung - ({event.date}) - {event.title}\n\n{chunk}")

# split_protokoll()

add_source({
    "source_name": "Protokoll des Gemeinderates Zelking-Matzleinsdorf",
    "source_directory": "gemeinderat",
    "website": "http://www.zelking-matzleinsdorf.gv.at/protokolle.html",
    # "crawling_force": True,
    "crawling_function": scrape_ff_man,
})
