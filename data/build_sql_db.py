import os
import json
import sqlite3


def build_sql_db():
    """
    Create a SQLite database and insert all data from people_knowledge and organisation_knowledge.

    The database will have two tables:
    - knowledge_destination: Stores people and organizations with their name as primary key
    - knowledge: Stores knowledge sentences with references to destinations
    """
    print("Building SQL database...")

    # Define the database path
    db_path = os.path.join(os.path.dirname(__file__), "knowledge.db")

    # Create a connection to the database (will create it if it doesn't exist)
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Create tables if they don't exist
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS knowledge_destination (
        name TEXT PRIMARY KEY,
        is_person BOOLEAN NOT NULL,
        is_organisation BOOLEAN NOT NULL
    )
    ''')

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS knowledge (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        knowledge_sentence TEXT NOT NULL,
        destination_name TEXT NOT NULL,
        FOREI<PERSON>N KEY (destination_name) REFERENCES knowledge_destination (name)
    )
    ''')

    print("Tables created successfully")

    # Create indexes for better performance
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_knowledge_destination_name ON knowledge_destination (name)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_knowledge_destination ON knowledge (destination_name)')

    print("Indexes created successfully")

    # Process people knowledge
    process_people_knowledge(conn)

    # Process organisation knowledge
    process_organisation_knowledge(conn)

    # Commit changes and close connection
    conn.commit()
    conn.close()

    print(f"SQL database created successfully at {db_path}")


def process_people_knowledge(conn: sqlite3.Connection):
    """Process people knowledge data and insert into the database."""
    print("Processing people knowledge")

    cursor = conn.cursor()

    # Path to people knowledge data
    people_data_path = os.path.join(os.path.dirname(__file__), "people_knowledge/data.json")

    if not os.path.exists(people_data_path):
        print(f"Warning: People knowledge data file not found at {people_data_path}")
        return

    # Load people knowledge data
    with open(people_data_path, 'r') as f:
        people_data = json.load(f)

    # Process each person
    for person in people_data:
        firstname = person.get('firstname', '')
        lastname = person.get('lastname', '')

        # Create full name in format "{Firstname} {Lastname}"
        full_name = f"{firstname} {lastname}".strip()

        if not full_name:
            continue

        # Insert person into knowledge_destination table
        try:
            cursor.execute(
                'INSERT OR REPLACE INTO knowledge_destination (name, is_person, is_organisation) VALUES (?, ?, ?)',
                (full_name, True, False)
            )
        except sqlite3.Error as e:
            print(f"Error inserting person {full_name}: {e}")
            continue

        # Process knowledge sentences
        knowledge = person.get('knowledge', "")

        try:
            cursor.execute(
                'INSERT INTO knowledge (knowledge_sentence, destination_name) VALUES (?, ?)',
                (knowledge, full_name)
            )
        except sqlite3.Error as e:
            print(f"Error inserting knowledge for {full_name}: {e}")

def process_organisation_knowledge(conn: sqlite3.Connection):
    """Process organisation knowledge data and insert into the database."""
    cursor = conn.cursor()

    # Path to organisation knowledge data
    org_data_path = os.path.join(os.path.dirname(__file__), "organisation_knowledge/data.json")

    if not os.path.exists(org_data_path):
        print(f"Warning: Organisation knowledge data file not found at {org_data_path}")
        return

    # Load organisation knowledge data
    with open(org_data_path, 'r') as f:
        org_data = json.load(f)

    # Process each organisation
    for org in org_data:
        name = org.get('name', '').strip()

        if not name:
            continue

        # Insert organisation into knowledge_destination table
        try:
            cursor.execute(
                'INSERT OR REPLACE INTO knowledge_destination (name, is_person, is_organisation) VALUES (?, ?, ?)',
                (name, False, True)
            )
        except sqlite3.Error as e:
            print(f"Error inserting organisation {name}: {e}")
            continue

        # Process knowledge sentences
        knowledge_entries = org.get('knowledge_sentences_about_that_organisation', [])

        for knowledge_entry in knowledge_entries:
            source = knowledge_entry.get('source', 'unknown')
            knowledge_sentences = knowledge_entry.get('knowledge', [])

            for sentence in knowledge_sentences:
                if not sentence.strip():
                    continue

                try:
                    cursor.execute(
                        'INSERT INTO knowledge (knowledge_sentence, destination_name) VALUES (?, ?)',
                        (sentence, name)
                    )
                except sqlite3.Error as e:
                    print(f"Error inserting knowledge for {name}: {e}")


if __name__ == "__main__":
    build_sql_db()