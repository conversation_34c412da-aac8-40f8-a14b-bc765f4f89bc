# Knowledge SQL Database

This document describes the SQLite database that stores knowledge about people and organizations extracted from various sources.

## Overview

The knowledge database (`knowledge.db`) is a SQLite database that contains information about people and organizations mentioned in various documents. It serves as a structured repository for facts and information that can be queried programmatically.

## Database Structure

The database consists of two main tables:

### 1. `knowledge_destination` Table

This table stores information about entities (people and organizations) that are the subjects of knowledge.

**Schema:**
```sql
CREATE TABLE knowledge_destination (
    name TEXT PRIMARY KEY,
    is_person BOOLEAN NOT NULL,
    is_organisation BOOLEAN NOT NULL
)
```

**Fields:**
- `name`: The name of the person or organization (Primary Key)
  - For people: Format is "{Firstname} {Lastname}"
  - For organizations: Full organization name
- `is_person`: Boolean flag indicating if the entity is a person
- `is_organisation`: Boolean flag indicating if the entity is an organization

**Indexes:**
- `idx_knowledge_destination_name`: Index on the `name` field for faster lookups

### 2. `knowledge` Table

This table stores individual knowledge sentences about people and organizations.

**Schema:**
```sql
CREATE TABLE knowledge (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    knowledge_sentence TEXT NOT NULL,
    destination_name TEXT NOT NULL,
    FOREIGN KEY (destination_name) REFERENCES knowledge_destination (name)
)
```

**Fields:**
- `id`: Auto-incremented unique identifier
- `knowledge_sentence`: The actual knowledge/fact about the entity
- `destination_name`: Foreign key to `knowledge_destination.name` - identifies which entity this knowledge is about

**Indexes:**
- `idx_knowledge_destination`: Index on the `destination_name` field for faster joins

## Data Sources

The database is populated from two main JSON files:

1. **People Knowledge**: `data/people_knowledge/data.json`
   - Contains information about people
   - Each entry includes firstname, lastname, title, and knowledge sentences

2. **Organization Knowledge**: `data/organisation_knowledge/data.json`
   - Contains information about organizations
   - Each entry includes name and knowledge sentences

## Database Creation Process

The database is created by the `build_sql_db()` function in `data/build_sql_db.py`. The process follows these steps:

1. Create the SQLite database file if it doesn't exist
2. Create the tables and indexes if they don't exist
3. Process people knowledge:
   - Read the people knowledge JSON file
   - For each person:
     - Create a full name in the format "{Firstname} {Lastname}"
     - Insert the person into the `knowledge_destination` table
     - Insert each knowledge sentence into the `knowledge` table
4. Process organization knowledge:
   - Read the organization knowledge JSON file
   - For each organization:
     - Insert the organization into the `knowledge_destination` table
     - Insert each knowledge sentence into the `knowledge` table

## Example Queries

### Get all people in the database:
```sql
SELECT name FROM knowledge_destination WHERE is_person = 1;
```

### Get all organizations in the database:
```sql
SELECT name FROM knowledge_destination WHERE is_organisation = 1;
```

### Get all knowledge about a specific person:
```sql
SELECT knowledge_sentence
FROM knowledge
WHERE destination_name = 'John Doe';
```

### Get all knowledge about a specific organization:
```sql
SELECT knowledge_sentence
FROM knowledge
WHERE destination_name = 'Example Organization';
```

### Count knowledge sentences by entity type:
```sql
SELECT
    SUM(CASE WHEN d.is_person = 1 THEN 1 ELSE 0 END) as people_knowledge_count,
    SUM(CASE WHEN d.is_organisation = 1 THEN 1 ELSE 0 END) as org_knowledge_count
FROM knowledge k
JOIN knowledge_destination d ON k.destination_name = d.name;
```

## Usage in Code

Here's an example of how to connect to and query the database in Python:

```python
import sqlite3
import os

# Connect to the database
db_path = os.path.join(os.path.dirname(__file__), "knowledge.db")
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Example: Get all knowledge about a specific person
person_name = "John Doe"
cursor.execute(
    "SELECT knowledge_sentence FROM knowledge WHERE destination_name = ?",
    (person_name,)
)
results = cursor.fetchall()

for sentence in results:
    print(f"- {sentence[0]}")

# Close the connection when done
conn.close()
```

## Maintenance

To rebuild the database with updated knowledge:

1. Update the source JSON files (`data/people_knowledge/data.json` and `data/organisation_knowledge/data.json`)
2. Run the `build_sql_db()` function:
   ```python
   from data.build_sql_db import build_sql_db
   build_sql_db()
   ```

This will recreate the database with the latest knowledge from the source files.
