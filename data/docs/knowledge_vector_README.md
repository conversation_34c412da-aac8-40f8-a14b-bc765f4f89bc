# Knowledge Vector Database

This document describes the vector database that stores embeddings of crawled and processed documents for semantic search capabilities.

## Overview

The vector database (`milvus.db`) is a Milvus database that contains vector embeddings of documents crawled from various sources. It enables semantic search functionality, allowing users to find relevant documents based on the meaning of their queries rather than just keyword matching.

## Database Structure

The database consists of a single collection called `searchable_collection` with the following fields:

### `searchable_collection` Collection

**Fields:**
- `id` (INT64, Primary Key): Auto-incremented unique identifier
- `vector` (FLOAT_VECTOR): The embedding vector of the document chunk (dimension: 1536 for OpenAI embeddings)
- `title` (VARCHAR): The title of the document
- `sourcePath` (VARCHAR): The path to the source metadata file
- `sourceName` (VARCHAR): The name of the original source file
- `sourceDate` (VARCHAR): The date of the document in ISO format (if available)

The collection is configured with `auto_id=True` and `enable_dynamic_field=True` to allow for automatic ID generation and dynamic field addition.

## Data Sources

The vector database is populated from raw crawled data stored in the following directories:

1. `data/raw/gemeinde`: Documents from the municipality website
2. `data/raw/ffmannersdorf`: Documents from the Mannersdorf fire department
3. `data/raw/ffmatzleinsdorf`: Documents from the Matzleinsdorf fire department
4. `data/raw/ffzelking`: Documents from the Zelking fire department

Each directory contains JSON metadata files that follow the `EmbeddingMetadata` model structure, along with corresponding embedding files.

## Embedding Process

Before the vector database is built, documents go through a pipeline:

1. **Crawling**: Web pages and documents are crawled and stored as HTML or their original format
2. **Markdown Conversion**: Original documents are converted to markdown format
3. **Classification**: Markdown documents are processed to extract metadata (date, title, description)
4. **Embedding**: Documents are split into chunks and embedded using OpenAI's text-embedding-3-small model

The embedding process creates vector representations of document chunks, which are then stored in the vector database.

## Database Creation Process

The database is created by the `build_vector_db()` function in `data/build_vector_db.py`. The process follows these steps:

1. Create a new Milvus collection schema with the necessary fields
2. Drop the existing collection if it exists
3. Create a new collection with the OpenAI embedding dimension (1536)
4. For each source directory:
   - Find all JSON metadata files
   - Load and validate each metadata file as an `EmbeddingMetadata` object
   - Load the corresponding embedding files
   - Insert the embeddings and metadata into the collection

## Search Functionality

The vector database is used for semantic search through the following process:

1. A user query is converted to an embedding vector using the Mistral embedding model
2. The query vector is compared to all document vectors in the database using cosine similarity
3. The most similar documents are returned, along with their metadata
4. Optional filtering can be applied using metadata fields (e.g., source path, date)
5. Results can be reranked using the BGE reranker model for improved relevance

## Example Search Query

Here's an example of how to search the vector database in Python:

```python
from search.Search import all_search

# Simple search
results = all_search("Öffnungszeiten Gemeindeamt", None, 5)

# Search with filter
results = all_search(
    "Öffnungszeiten Gemeindeamt", 
    "sourcePath like 'data/documents/gemeinde_webseite%'", 
    5
)

# Process results
for result in results:
    print(f"Title: {result['title']}")
    print(f"Source: {result['sourceName']}")
    print(f"Date: {result['sourceDate']}")
    print(f"Content: {result['text'][:100]}...")  # Show first 100 chars
    print("---")
```

## Advanced RAG Implementation

The system implements an advanced Retrieval-Augmented Generation (RAG) approach:

1. **Initial Search**: The user query is used to retrieve an initial set of documents
2. **Hypothetical Questions**: The system generates hypothetical questions based on the initial results
3. **Expanded Search**: These questions are used to retrieve additional relevant documents
4. **Reranking**: All retrieved documents are reranked based on relevance to the original query
5. **Result Presentation**: The most relevant documents are presented to the user

This approach helps overcome limitations of simple vector search by expanding the retrieval context through hypothetical questions.

## Embedding Model

The system uses two embedding models:

1. **OpenAI text-embedding-3-small**: Used during the document processing pipeline to create document embeddings (1536 dimensions)
2. **Mistral Embed**: Used at query time to convert user queries to embeddings

## Maintenance

To rebuild the vector database with updated documents:

1. Process new documents through the crawling, markdown conversion, classification, and embedding pipeline
2. Run the `build_vector_db()` function:
   ```python
   from data.build_vector_db import build_vector_db
   build_vector_db()
   ```

This will recreate the database with all the latest document embeddings.

## Relationship to Knowledge SQL Database

While the vector database stores raw document embeddings for semantic search, the Knowledge SQL Database (`knowledge.db`) stores structured information about people and organizations extracted from these documents. The two databases serve complementary purposes:

- **Vector Database**: Enables semantic search across all document content
- **SQL Database**: Provides structured access to specific knowledge about entities

Together, they form a comprehensive knowledge system that supports both free-form semantic search and structured entity-based queries.
