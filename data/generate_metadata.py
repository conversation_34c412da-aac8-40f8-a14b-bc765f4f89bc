from enum import Enum
from typing import Optional
from ollama import chat
from ollama import ChatResponse
from pydantic import BaseModel
from . import Metadata, metadata_system_propmt


with open("data/documents/gemeinderat/P_GR02.05.2024.md_chunk_0.txt", "r") as file:
    protocol = file.read()

    response = chat(
        model='mistral',
        messages=[{
            "role": "system",
            "content": metadata_system_propmt
        }, {
            "role": "user",
            "content": protocol
        }],
        options={"temperature": 0.0},
        format=Metadata.model_json_schema()
    )

    data = Metadata.model_validate_json(response.message.content)
    print(data)
