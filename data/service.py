import os
import json
import sqlite3
from typing import List, Optional
from pymilvus import MilvusClient
from search.Search import ResultItem
from openai import OpenAI

client = OpenAI()

# Define the database path
db_path = os.path.join(os.path.dirname(__file__), "knowledge.db")
milvus_client = MilvusClient(os.path.join(os.path.dirname(__file__), "milvus.db"))

def getAvailablePeople() -> List[str]:
    # Create a connection to the database (will create it if it doesn't exist)
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    res = cursor.execute('SELECT name FROM knowledge_destination WHERE is_person = 1')
    people = [resitem[0] for resitem in res.fetchall()]
    return people

def getAvailableOrganisations() -> List[str]:
    # Create a connection to the database (will create it if it doesn't exist)
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    res = cursor.execute('SELECT name FROM knowledge_destination WHERE is_organisation = 1')
    orgas = [resitem[0] for resitem in res.fetchall()]
    return orgas

def getKnowledge(name: str) -> List[str]:
    # Create a connection to the database (will create it if it doesn't exist)
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    res = cursor.execute(f"SELECT knowledge_sentence FROM knowledge WHERE destination_name = '{name}'")

    if res.fetchone() is None:
        return []

    knowledge = [resitem[0] for resitem in res.fetchall()]
    return knowledge


def semanticSearch(
    query: str,
    filter: Optional[str],
    limit: int = 3) -> list[ResultItem]:

    all_results = []

    # get query embedding
    text = query.replace("\n", " ")
    response = client.embeddings.create(
        model="text-embedding-3-small",
        input=text
    )
    query_vector = response.data[0].embedding

    # query the milvus database
    res = milvus_client.search(
        collection_name="searchable_collection",
        data=[query_vector],
        limit=limit,
        filter=filter,
        output_fields=["sourcePath", "sourceName", "title", "sourceDate"]
    )
    all_results.extend(res[0])

    # remove duplicates from all_results
    seen = set()
    unique_res = []
    for obj in all_results:
        value = obj['entity']['sourcePath']
        if value not in seen:
            seen.add(value)
            unique_res.append(obj)
    
    
    # get the full parent for each chunk 
    files: list[ResultItem] = [item["entity"] for item in res[0]]
    for file in files:
        with open(file['sourcePath'], "r") as sourceFile:
            path = file['sourcePath'].split('/')[:-1]
            metadata = json.load(sourceFile)
            markdown_file_name = metadata["markdown_file"]
            markdown_file_path = os.path.join('/'.join(path), markdown_file_name) 

            print(f"read markdown file {markdown_file_path}")
            with open(markdown_file_path, 'r', encoding='utf-8') as markdownFile:
                file['text'] = markdownFile.read()
    
    return files
