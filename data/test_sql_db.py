import os
import sqlite3
from build_sql_db import build_sql_db

def test_sql_db():
    """Test the SQL database creation and query the data."""
    # Build the database
    build_sql_db()
    
    # Define the database path
    db_path = os.path.join(os.path.dirname(__file__), "knowledge.db")
    
    # Check if the database file exists
    if not os.path.exists(db_path):
        print(f"Error: Database file not found at {db_path}")
        return
    
    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Query and print some statistics
    cursor.execute("SELECT COUNT(*) FROM knowledge_destination WHERE is_person = 1")
    people_count = cursor.fetchone()[0]
    print(f"Number of people: {people_count}")
    
    cursor.execute("SELECT COUNT(*) FROM knowledge_destination WHERE is_organisation = 1")
    org_count = cursor.fetchone()[0]
    print(f"Number of organizations: {org_count}")
    
    cursor.execute("SELECT COUNT(*) FROM knowledge")
    knowledge_count = cursor.fetchone()[0]
    print(f"Total knowledge sentences: {knowledge_count}")
    
    # Print some sample data
    print("\nSample people:")
    cursor.execute("""
    SELECT name FROM knowledge_destination 
    WHERE is_person = 1 
    LIMIT 5
    """)
    for row in cursor.fetchall():
        print(f"- {row[0]}")
    
    print("\nSample organizations:")
    cursor.execute("""
    SELECT name FROM knowledge_destination 
    WHERE is_organisation = 1 
    LIMIT 5
    """)
    for row in cursor.fetchall():
        print(f"- {row[0]}")
    
    print("\nSample knowledge sentences:")
    cursor.execute("""
    SELECT k.knowledge_sentence, k.source, d.name 
    FROM knowledge k
    JOIN knowledge_destination d ON k.destination_name = d.name
    LIMIT 5
    """)
    for row in cursor.fetchall():
        print(f"- {row[2]}: {row[0]} (Source: {row[1]})")
    
    # Close the connection
    conn.close()

if __name__ == "__main__":
    test_sql_db()
