import asyncio
import json
import os
import shutil
from typing import Callable, TypedDict, Optional
from pydantic import BaseModel
from mistralai import Mistral
from dotenv import load_dotenv

from ollama import AsyncClient, generate
from ollama import ChatResponse


from data.tools.add_source import scrape_website
from data.tools.add_source import run_marker


def load_env_variable(variable_name: str) -> None:
    """
    Loads a variable from the .env file and sets it as an environment variable
    if it's not already set.

    Args:
        variable_name: The name of the variable to load.
    """
    load_dotenv()  # Load variables from .env file

    if variable_name not in os.environ:
        value = os.getenv(variable_name)
        if value is not None:
            os.environ[variable_name] = value
            print(f"Loaded {variable_name} from .env and set as environment variable.")
        else:
            print(f"{variable_name} not found in .env.")
    else:
        print(f"{variable_name} already set as environment variable.")

# load_env_variable("MISTRAL_API_KEY")
# load_env_variable("GOOGLE_API_URL")

reader_lm_available = False

# try:
#     response: ChatResponse = generate(model='reader-lm', prompt="<html><body><h1>Hello</h1></body></html>")
#     reader_lm_available = True
# except Exception as e:
#     reader_lm_available = False



# Initialize Mistral
mistral_client = Mistral(
    api_key=os.environ.get("MISTRAL_API_KEY"),
)



# In this script, we will perform the steps that are described in the README.md
# All steps that are already done will be skipped

# In short this is:
# 1. Scrape a website
# 2. Run the scraped documents through marker
# 3. Run the documents through an llm to get metadata
# 4. Embed the documents and store them in a pandas dataframe csv
# 5. Move to storage
# 6. Remove all tmp files

class Config(TypedDict):
    # The name of the source, for example "gemeinderat"
    source_name: str
    # The directory where the documents are stored, used in this path "data/documents/[source_name]"
    source_directory: str
    # The website to scrape
    website: str
    # always for the crawling function
    crawling_force: bool
    # the target css selectors while crawling
    crawling_target_content: Optional[list[str]]
    # The crawling function to use
    crawling_function: Callable[[str], bool] | None
    embedding_function: Callable[[], bool] | None

step_tmp_folders = [
    "data/tmp/1_scrape_html",
    "data/tmp/2_marker",
    "data/tmp/3_metadata",
    "data/tmp/4_embedding",
]

def add_source(config: Config):
    step_tmp_folders_exist = [
        os.path.exists("data/tmp/1_scrape_html"),
        os.path.exists("data/tmp/2_marker"),
        os.path.exists("data/tmp/3_metadata"),
        os.path.exists("data/tmp/4_embedding"),
    ]

    # Scraping should always be run with a custom crawling function
    # Scraping should be skipped if the tmp/1_scrape_html folder exists and there is no custom crawling function
    if (step_tmp_folders_exist[0] is False and any(step_tmp_folders_exist[1:4]) is False) or ('crawling_force' in config and config["crawling_force"] is True):
        if "crawling_function" in config and config["crawling_function"] is not None:
            print("Scraping website")
            config["crawling_function"](config)
        else:
            print("Scraping website")
            scrape_website(config)
    
    # If the marker folder exists, but non of the upcoming, we will still run marker
    if any(step_tmp_folders_exist[2:4]) is False:
        run_marker(config)

    if step_tmp_folders_exist[2] is False and any(step_tmp_folders_exist[3:4]) is False:
        generate_metadata(config)

    if step_tmp_folders_exist[3] is False:
        if 'embedding_function' in config and config["embedding_function"] is not None:
            config["embedding_function"]()
        else:
            embed_documents(config)

    if step_tmp_folders_exist[3]:
        move_to_storage(config)

    # remove_tmp_files()
    
    


def generate_metadata(config: Config):
    print("Generating metadata")

    os.makedirs(os.path.join(os.path.dirname(__file__), "../tmp/3_metadata"), exist_ok=True)
    base_path = os.path.join(os.path.dirname(__file__), "../tmp/2_marker")
    files = os.listdir(base_path)
    # filter out all the files that do not end with .md
    # files = [file for file in files if file.endswith(".md")]
    
    # Run the async function in the event loop
    asyncio.run(process_files_with_metadata(files, base_path))
    pass

def embed_documents(config: Config):
    print("Embedding documents")

    os.makedirs(os.path.join(os.path.dirname(__file__), "../tmp/4_embedding"), exist_ok=True)

    # Loop through all the files in the tmp/2_marker folder
    all_files = os.listdir(os.path.join(os.path.dirname(__file__), "../tmp/2_marker"))
    for index, file in enumerate(all_files):
        print(f"Embedding {index+1}/{len(all_files)}")
        # If the file already exists in the tmp/4_embedding folder, skip it
        filename = os.path.splitext(file)[0]

        # check if there are files that start with the same filename and end with .txt
        # if so, create a list of all those files in chunks
        chunks = [f for f in os.listdir(os.path.join(os.path.dirname(__file__), "../tmp/2_marker")) if f.startswith(filename) and f.endswith(".txt")]
        

        if os.path.exists(os.path.join(os.path.dirname(__file__), "../tmp/4_embedding", f"{filename}-1" + '.txt')):
            print(f"Skipping {file} because it already has embeddings")
            continue

        print(f"Embedding {file}")

        # Read the markdown file
        if os.path.exists(os.path.join(os.path.dirname(__file__), "../tmp/2_marker", filename + '.md')):
            with open(os.path.join(os.path.dirname(__file__), "../tmp/2_marker", filename + '.md'), "r") as f:
                text = f.read()
        elif os.path.exists(os.path.join(os.path.dirname(__file__), "../tmp/2_marker", filename + '.txt')):
            with open(os.path.join(os.path.dirname(__file__), "../tmp/2_marker", filename + '.txt'), "r") as f:
                text = f.read()

        # Read the metadata file
        with open(os.path.join(os.path.dirname(__file__), "../tmp/3_metadata", filename + '.json'), "r") as f:
            metadata = json.load(f)

        if(len(text.strip()) < 1):
            print(f"Skipping {file} because it is empty")
            continue

        # Embed the text
        embeddings = asyncio.run(embed_text(text, metadata["title"] if metadata is not None else None))

        chunk_files: list[str] = []
        for index, embedding in enumerate(embeddings):
            # Save the embeddings to a file
            chunk_file_name = f"{filename}-{index+1}.txt"
            chunk_files.append(chunk_file_name)
            with open(os.path.join(os.path.dirname(__file__), "../tmp/4_embedding", chunk_file_name), "w") as f:
                f.write(str(embedding))
        
        # updateh the metadata
        metadata['markdown'] = file
        metadata['embedding_files'] = chunk_files

        # write the updated metadata back
        with open(os.path.join(os.path.dirname(__file__), "../tmp/3_metadata", filename + '.json'), "w") as f:
            json.dump(metadata, f)

    pass

def move_to_storage(config: Config):
    print('Moveing files to storage')
    source_directory = config["source_directory"]
    storage_path = os.path.join(os.path.dirname(__file__), f"../documents/{source_directory}")

    # Create storage directory if it doesn't exist
    os.makedirs(storage_path, exist_ok=True)

    # Move files from tmp/4_embedding to storage
    embedding_path = os.path.join(os.path.dirname(__file__), "../tmp/4_embedding")
    for file in os.listdir(embedding_path):
        shutil.move(os.path.join(embedding_path, file), os.path.join(storage_path, file))

    # Move files from tmp/3_metadata to storage
    metadata_path = os.path.join(os.path.dirname(__file__), "../tmp/3_metadata")
    for file in os.listdir(metadata_path):
        shutil.move(os.path.join(metadata_path, file), os.path.join(storage_path, file))

    # Move files from tmp/2_marker to storage
    marker_path = os.path.join(os.path.dirname(__file__), "../tmp/2_marker")
    for file in os.listdir(marker_path):
        shutil.move(os.path.join(marker_path, file), os.path.join(storage_path, file))

    # Remove temporary folders
    for folder in step_tmp_folders:
        if os.path.exists(folder):
            shutil.rmtree(folder)

def remove_tmp_files():
    for folder in step_tmp_folders:
        if os.path.exists(folder):
            shutil.rmtree(folder)

# UTILS

# load a html file, convert it to markdown and return the content
def html_to_md(file_path: str) -> str:
    with open(file_path, "r") as f:
        html_input = f.read()
        try:
            # Run the async function in the event loop
            response = asyncio.run(reader_lm(html_input))
            return response
        except asyncio.TimeoutError:
            print(f"Timeout occurred while processing {file_path}")
            return None
        except Exception as e:
            print(f"Error processing {file_path}: {str(e)}")
            return None

# Convert html to markdown using a generate call to ollame with model reader-lm
async def reader_lm(html_input: str) -> str:
    async def call_ollama_reader() -> str:
        model = 'reader-lm'
        # temperature 0 with give a better result
        response = await AsyncClient().generate(model, prompt=html_input, options={"temperature": 0.0})
        return response.response
    
    # Wait for reader_lm with a 30 second timeout
    return await asyncio.wait_for(call_ollama_reader(), timeout=30)


# metadata utils
async def process_files_with_metadata(files: list[str], base_path: str):
    for index, file in enumerate(files):
        print(f"Metadata {index}/{len(files)}")
        # Skip files that already have metadata
        if os.path.exists(os.path.join(os.path.dirname(__file__), "../tmp/3_metadata", os.path.splitext(file)[0] + '.json')):
            print(f"Skipping {file} because it already has metadata")
            continue

        with open(os.path.join(base_path, file), "r") as f:
            print(f"Generating metadata for {file}")
            if 'chunk' in file:
                original = file.split('_chunk_')[0]
            text = f.read()
            try:
                response = await generate_metadata_with_llm(file, text[:1000])
                metadata_json = {
                    'title': response.title,
                    'date': response.date,
                }

                if original and response is not None:
                    metadata_json['sourceFile'] = original

                json_file_name = os.path.splitext(file)[0] + '.json'
                with open(os.path.join(os.path.dirname(__file__), "../tmp/3_metadata", json_file_name), "w") as f:
                    json.dump(metadata_json, f)
                
                original = None
            except Exception as e:
                original = None
                print(f"Error processing {file}: {str(e)}")

class Metadata(BaseModel):
    title: str
    date: Optional[str]

async def generate_metadata_with_llm(filename: str, text: str):
    # Add 2 second delay before each API call
    await asyncio.sleep(2)
    
    response = mistral_client.chat.parse(
        model="mistral-small-latest",
        messages=[
            {"role": "system", "content": """
             You are an assistant editor, who looks at files.
             You generate a title for the document and search for the creation date in the provided filename and content.
             All files are in german. The title must be in german, the date can either be an ISO Datestring or empty.
             To source the date, you must only use the filename or headlines within the content.
             Sometimes a date is mentioned in the Content, that describes and event but is not the creation date of the document.
             Sometimes a date is mentioned with a month written as name. Example: 01. August 2025 => 2025-08-01
             Only provide the date if you are absolutely sure about it. A wrong date can kill people and get me fired.
             """},
            {"role": "user", "content": f"Generate the title and date for the following document:\n\nFilename: {filename}\n\nContent:\n{text}"}
        ],
        response_format=Metadata,
        max_tokens=256,
        temperature=0.0
    )

    return response.choices[0].message.parsed

# embedding utils
async def embed_text(text: str, title: str | None = None):
    stripped_text = text.strip().replace(" ", "")

    await asyncio.sleep(2)

    batch_size = 18000
    chunk_size = 10000

    embedded_chunks: list[str] = []

    for i in range(0, len(stripped_text), batch_size):
        batch = stripped_text[i:i+batch_size]

        embeding_chunks = [
            f"{title}\n{batch[i:i+chunk_size]}"
            for i in range(0, len(batch), chunk_size)
        ]

        # embed the documents with mistral
        response = mistral_client.embeddings.create(
            model="mistral-embed",
            inputs=embeding_chunks
        )

        for embedding in response.data:
            embedded_chunks.append(embedding.embedding)

    return embedded_chunks
    #[0].embedding

    # embed the text with a call to ollama with the model "stanus74/e5-base-sts-en-de:latest"
    # response = await AsyncClient().embed(model="nomic-embed-text:latest", input=text)
    # return response.embeddings[0]
