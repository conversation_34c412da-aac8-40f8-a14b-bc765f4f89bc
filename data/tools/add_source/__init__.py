import asyncio
import json
import os
import shutil
from typing import Callable, TypedDict, Optional
from mistralai import Mistral
from dotenv import load_dotenv

from .scrape_website import scrape_website
from .marker import run_marker
from .metadata import generate_metadata
from .embedding import embed_documents

tmp_path = os.path.join(os.path.dirname(__file__), "../../tmp")

def load_env_variable(variable_name: str) -> None:
    """
    Loads a variable from the .env file and sets it as an environment variable
    if it's not already set.

    Args:
        variable_name: The name of the variable to load.
    """
    load_dotenv()  # Load variables from .env file

    if variable_name not in os.environ:
        value = os.getenv(variable_name)
        if value is not None:
            os.environ[variable_name] = value
            print(f"Loaded {variable_name} from .env and set as environment variable.")
        else:
            print(f"{variable_name} not found in .env.")
    else:
        print(f"{variable_name} already set as environment variable.")

# load_env_variable("MISTRAL_API_KEY")
# load_env_variable("GOOGLE_API_URL")

# Initialize Mistral
mistral_client = Mistral(
    api_key=os.environ.get("MISTRAL_API_KEY"),
)

class Config(TypedDict):
    # The name of the source, for example "gemeinderat"
    source_name: str
    # The directory where the documents are stored, used in this path "data/documents/[source_name]"
    source_directory: str
    # The website to scrape
    website: str
    # always for the crawling function
    crawling_force: bool
    # the target css selectors while crawling
    crawling_target_content: Optional[list[str]]
    # The crawling function to use
    crawling_function: Callable[[str], bool] | None
    embedding_function: Callable[[], bool] | None

html_path = os.path.join(tmp_path, "1_scrape_html")
marker_path = os.path.join(tmp_path, "2_marker")
metadata_path = os.path.join(tmp_path, "3_metadata")
embedding_path = os.path.join(tmp_path, "4_embedding")

step_tmp_folders = [
    html_path,
    marker_path,
    metadata_path,
    embedding_path,
]

def add_source(config: Config):
    step_tmp_folders_exist = [
        os.path.exists(html_path),
        os.path.exists(marker_path),
        os.path.exists(metadata_path),
        os.path.exists(embedding_path),
    ]

    # Scraping should always be run with a custom crawling function
    # Scraping should be skipped if the tmp/1_scrape_html folder exists and there is no custom crawling function
    if (step_tmp_folders_exist[0] is False and any(step_tmp_folders_exist[1:4]) is False) or ('crawling_force' in config and config["crawling_force"] is True):
        if "crawling_function" in config and config["crawling_function"] is not None:
            print("Scraping website")
            config["crawling_function"](config)
        else:
            print("Scraping website")
            scrape_website(config)
    
    # If the marker folder exists, but non of the upcoming, we will still run marker
    if any(step_tmp_folders_exist[2:4]) is False:
        run_marker(config)

    if step_tmp_folders_exist[2] is False and any(step_tmp_folders_exist[3:4]) is False:
        generate_metadata(config)

    if step_tmp_folders_exist[3] is False:
        if 'embedding_function' in config and config["embedding_function"] is not None:
            config["embedding_function"]()
        else:
            embed_documents(config)

    if step_tmp_folders_exist[3]:
        move_to_storage(config)



def move_to_storage(config: Config):
    print('Moveing files to storage')
    source_directory = config["source_directory"]
    storage_path = os.path.join(os.path.dirname(__file__), f"../documents/{source_directory}")

    # Create storage directory if it doesn't exist
    os.makedirs(storage_path, exist_ok=True)

    # Move files from tmp/4_embedding to storage
    embedding_path = os.path.join(os.path.dirname(__file__), "../tmp/4_embedding")
    for file in os.listdir(embedding_path):
        shutil.move(os.path.join(embedding_path, file), os.path.join(storage_path, file))

    # Move files from tmp/3_metadata to storage
    metadata_path = os.path.join(os.path.dirname(__file__), "../tmp/3_metadata")
    for file in os.listdir(metadata_path):
        shutil.move(os.path.join(metadata_path, file), os.path.join(storage_path, file))

    # Move files from tmp/2_marker to storage
    marker_path = os.path.join(os.path.dirname(__file__), "../tmp/2_marker")
    for file in os.listdir(marker_path):
        shutil.move(os.path.join(marker_path, file), os.path.join(storage_path, file))

    # Remove temporary folders
    for folder in step_tmp_folders:
        if os.path.exists(folder):
            shutil.rmtree(folder)