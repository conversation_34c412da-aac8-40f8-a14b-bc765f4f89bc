import os
import async<PERSON>
import json

from mistralai import Mistral
from . import Config, tmp_path, marker_path, embedding_path, metadata_path

# Initialize Mistral
mistral_client = Mistral(
    api_key=os.environ.get("MISTRAL_API_KEY"),
)


def embed_documents(config: Config):
    print("Embedding documents")

    os.makedirs(embedding_path, exist_ok=True)

    # Loop through all the files in the tmp/2_marker folder
    all_files = os.listdir(marker_path)
    for index, file in enumerate(all_files):
        print(f"Embedding {index+1}/{len(all_files)}")
        # If the file already exists in the tmp/4_embedding folder, skip it
        filename = os.path.splitext(file)[0]

        if os.path.exists(os.path.join(embedding_path, f"{filename}-1" + '.txt')):
            print(f"Skipping {file} because it already has embeddings")
            continue

        print(f"Embedding {file}")

        # Read the markdown file
        if os.path.exists(os.path.join(marker_path, filename + '.md')):
            with open(os.path.join(marker_path, filename + '.md'), "r") as f:
                text = f.read()
        elif os.path.exists(os.path.join(marker_path, filename + '.txt')):
            with open(os.path.join(marker_path, filename + '.txt'), "r") as f:
                text = f.read()

        # Read the metadata file
        with open(os.path.join(metadata_path, filename + '.json'), "r") as f:
            metadata = json.load(f)

        if(len(text.strip()) < 1):
            print(f"Skipping {file} because it is empty")
            continue

        # Embed the text
        embeddings = asyncio.run(embed_text(text, metadata["title"] if metadata is not None else None))

        chunk_files: list[str] = []
        for index, embedding in enumerate(embeddings):
            # Save the embeddings to a file
            chunk_file_name = f"{filename}-{index+1}.txt"
            chunk_files.append(chunk_file_name)
            with open(os.path.join(embedding_path, chunk_file_name), "w") as f:
                f.write(str(embedding))
        
        # updateh the metadata
        metadata['markdown'] = file
        metadata['embedding_files'] = chunk_files

        # write the updated metadata back
        with open(os.path.join(metadata_path, filename + '.json'), "w") as f:
            json.dump(metadata, f)

    pass

# embedding utils
async def embed_text(text: str, title: str | None = None):
    stripped_text = text.strip().replace(" ", "")

    await asyncio.sleep(2)

    batch_size = 18000
    chunk_size = 10000

    embedded_chunks: list[str] = []

    for i in range(0, len(stripped_text), batch_size):
        batch = stripped_text[i:i+batch_size]

        embeding_chunks = [
            f"{title}\n{batch[i:i+chunk_size]}"
            for i in range(0, len(batch), chunk_size)
        ]

        # embed the documents with mistral
        response = mistral_client.embeddings.create(
            model="mistral-embed",
            inputs=embeding_chunks
        )

        for embedding in response.data:
            embedded_chunks.append(embedding.embedding)

    return embedded_chunks