import os
from . import Config, html_path, marker_path

from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.output import text_from_rendered
from marker.config.parser import ConfigParser

config_parser = None
converter = None

# Initialize Marker
marker_config = {
    "output_format": "markdown",
    "disable_image_extraction": True,
    # "use_llm": True,
    # "gemini_api_key": os.environ.get("GOOGLE_API_KEY")
}

def run_marker(config: Config):
    print("Running markdown extraction")

    os.makedirs(marker_path, exist_ok=True)

    all_files = os.listdir(html_path)
    for index, file in enumerate(all_files):
        print(f"Marker {index+1}/{len(all_files)}")
        # if the file already exists, skip it
        file_name = f"{os.path.splitext(file)[0]}.md"
        if os.path.exists(os.path.join(marker_path, file_name)):
            print(f"Skipping {file} because it already exists")
            continue

        file_path = os.path.join(html_path, file)
        md_filename = os.path.splitext(file)[0] + '.md'
        md_text = None
        file_length = os.path.getsize(file_path)

        if md_text is None:
            print(f"Processing {file} with marker (file length: {file_length})")
            md_text = document_to_md(file_path)
        
        if md_text is not None and len(md_text) > 10:
            with open(os.path.join(marker_path, md_filename), "w") as f:
                f.write(md_text)

# Convert any document to markdown using marker
def document_to_md(file_path: str) -> str:
    global config_parser, converter

    if config_parser is None:
        config_parser = ConfigParser(marker_config)
    if converter is None:
        converter = PdfConverter(
            config=config_parser.generate_config_dict(),
            artifact_dict=create_model_dict(),
            llm_service=config_parser.get_llm_service()
        )

    rendered = converter(file_path)
    text, _, images = text_from_rendered(rendered)
    return text