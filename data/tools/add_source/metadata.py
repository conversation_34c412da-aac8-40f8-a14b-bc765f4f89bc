import os
import json
import asyncio
from typing import Op<PERSON>

from mistralai import Mistra<PERSON>
from pydantic import BaseModel
from . import Config, marker_path, metadata_path

# Initialize Mistral
mistral_client = Mistral(
    api_key=os.environ.get("MISTRAL_API_KEY"),
)

def generate_metadata(config: Config):
    print("Generating metadata")

    os.makedirs(metadata_path, exist_ok=True)
    files = os.listdir(marker_path)
    # filter out all the files that do not end with .md
    # files = [file for file in files if file.endswith(".md")]
    
    # Run the async function in the event loop
    asyncio.run(process_files_with_metadata(files, marker_path))
    pass

async def process_files_with_metadata(files: list[str], base_path: str):
    for index, file in enumerate(files):
        print(f"Metadata {index}/{len(files)}")
        # Skip files that already have metadata
        if os.path.exists(os.path.join(metadata_path, os.path.splitext(file)[0] + '.json')):
            print(f"Skipping {file} because it already has metadata")
            continue

        with open(os.path.join(base_path, file), "r") as f:
            print(f"Generating metadata for {file}")
            if 'chunk' in file:
                original = file.split('_chunk_')[0]
            text = f.read()
            try:
                response = await generate_metadata_with_llm(file, text[:1000])
                metadata_json = {
                    'title': response.title,
                    'date': response.date,
                }

                if original and response is not None:
                    metadata_json['sourceFile'] = original

                json_file_name = os.path.splitext(file)[0] + '.json'
                with open(os.path.join(metadata_path, json_file_name), "w") as f:
                    json.dump(metadata_json, f)
                
                original = None
            except Exception as e:
                original = None
                print(f"Error processing {file}: {str(e)}")

# metadata utils

class Metadata(BaseModel):
    title: str
    date: Optional[str]

async def generate_metadata_with_llm(filename: str, text: str):
    # Add 2 second delay before each API call
    await asyncio.sleep(2)
    
    response = mistral_client.chat.parse(
        model="mistral-small-latest",
        messages=[
            {"role": "system", "content": """
             You are an assistant editor, who looks at files.
             You generate a title for the document and search for the creation date in the provided filename and content.
             All files are in german. The title must be in german, the date can either be an ISO Datestring or empty.
             To source the date, you must only use the filename or headlines within the content.
             Sometimes a date is mentioned in the Content, that describes and event but is not the creation date of the document.
             Sometimes a date is mentioned with a month written as name. Example: 01. August 2025 => 2025-08-01
             Only provide the date if you are absolutely sure about it. A wrong date can kill people and get me fired.
             """},
            {"role": "user", "content": f"Generate the title and date for the following document:\n\nFilename: {filename}\n\nContent:\n{text}"}
        ],
        response_format=Metadata,
        max_tokens=256,
        temperature=0.0
    )

    return response.choices[0].message.parsed