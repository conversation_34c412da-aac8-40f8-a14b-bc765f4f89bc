from pymilvus import CollectionSchema, DataType, FieldSchema, MilvusClient
from typing import Optional
import os
import json
import ast

# gemeinderat_embeddings = pd.read_csv("data/gemeinderat/embeddings.csv")
# gemeinderat_embeddings = gemeinderat_embeddings[searchableDataframeColumns]
# gemeinderat_embeddings['embedding'] = gemeinderat_embeddings['embedding'].apply(eval).apply(np.array)

# gemeinde_webseiten_embeddings = pd.read_csv("data/gemeinde_webseite/embeddings.csv")
# gemeinde_webseiten_embeddings = gemeinde_webseiten_embeddings[searchableDataframeColumns]
# gemeinde_webseiten_embeddings['embedding'] = gemeinde_webseiten_embeddings['embedding'].apply(eval).apply(np.array)

# Create a new Milvus client with a local database
client = MilvusClient("data/milvus.db")

openai_embedding_dimension = 1536
mistral_embedding_dimension = 1024

# 1. Create a schema
id_field = FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, description="primary id")
vector_field = FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=mistral_embedding_dimension)
# document fields
title_field = FieldSchema(name="title", dtype=DataType.VARCHAR, max_length=65535)
source_path_field = FieldSchema(name="sourcePath", dtype=DataType.VARCHAR, max_length=65535)
source_name_field = FieldSchema(name="sourceName", dtype=DataType.VARCHAR, max_length=65535)
source_date_field = FieldSchema(name="sourceDate", dtype=DataType.VARCHAR, max_length=65535)

# attending people
# missing people

schema = CollectionSchema(
    fields=[
        id_field,
        vector_field,
        # document fields
        title_field,
        source_path_field,
        source_name_field,
        source_date_field
    ],
    auto_id=True,
    enable_dynamic_field=True,
)


# Drop the collection if it already exists
if client.has_collection(collection_name="searchable_collection"):
    client.drop_collection(collection_name="searchable_collection")

# Create a new collection with the given name and dimension
client.create_collection(
    collection_name="searchable_collection",
    auto_id=True,
    # schema=schema,
    dimension=mistral_embedding_dimension,  # The vectors we will use in this demo has 768 dimensions
)

sources = [
    'gemeinde_webseite',
    'gemeinderat',
    'ff_mannersdorf_webseite',
    'ff_zelking_webseite',
    'ff_matzleinsdorf_webseite'
]

def add_source(source_key: str):
    base_path = os.path.join(os.path.dirname(__file__), "../documents/", source_key)
    metadata_files = [f for f in os.listdir(base_path) if f.endswith('.json')]

    data = []

    for metadata_file in metadata_files:
        # Read the metadata file
        with open(os.path.join(base_path, metadata_file), "r") as f:
            metadata = json.load(f)
        
        if metadata is None:
            print(f"Skip {metadata_file} because it could not load json")
            continue

        print(f"Adding {metadata_file}")

        title: Optional[str] = metadata['title'] if 'title' in metadata else None
        date: Optional[str] = metadata['date'] if 'date' in metadata else None
        markdown_file: Optional[str] = metadata['markdown'] if 'markdown' in metadata else None
        embedding_files: list[str] = metadata['embedding_files'] if 'embedding_files' in metadata else None

        if title is None or markdown_file is None or embedding_files is None:
            print(f"Error in file {metadata_file}")
            continue

        embeddings: list[list[float]] = []

        for embedding_file in embedding_files:
            with open(os.path.join(base_path, embedding_file), "r") as f:
                embedding_raw_text = f.read()
                embedding = ast.literal_eval(embedding_raw_text)
                embeddings.append(embedding)

        newData = [
            {
                "vector": vector,
                # document fields
                "title": title,
                "sourcePath": f"data/documents/{source_key}/{markdown_file}",
                "sourceName": markdown_file,
                "sourceDate": date 
            }
            for vector in embeddings
        ]
        data.extend(newData)

    client.insert(collection_name="searchable_collection", data=data)


for source in sources:
    print(f"Add source {source}")
    add_source(source)

# Add a primary key field to the schema
# schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True, auto_id=True)
# The sourcePath field is the path of the source of the data
# schema.add_field(field_name="sourcePath", datatype=DataType.VARCHAR, is_primary=False, max_length=65535)
# The sourceName field is the name of the source of the data
# schema.add_field(field_name="sourceName", datatype=DataType.VARCHAR, is_primary=False, max_length=65535)
# The sourceType field is the type of the source of the data (Document, Webpage, etc.)
# schema.add_field(field_name="sourceType", datatype=DataType.VARCHAR, is_primary=False, max_length=65535)
# The sourceOrigin field is the origin of the source of the data (e.g. https://www.google.com)
# schema.add_field(field_name="sourceOrigin", datatype=DataType.VARCHAR, is_primary=False, max_length=65535)
# The crawlDate field is the date of the crawl of the data (ISO 8601)
# schema.add_field(field_name="crawlDate", datatype=DataType.VARCHAR, is_primary=False, max_length=65535)
# The sourceDate field is the date of the source of the data (ISO 8601)
# schema.add_field(field_name="sourceDate", datatype=DataType.VARCHAR, is_primary=False, max_length=65535)
# The vector field is the vector of the data
# schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, is_primary=False, dim=openai_embedding_dimension)
