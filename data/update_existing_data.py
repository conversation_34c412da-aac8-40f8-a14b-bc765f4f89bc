import os
import json
from openai import OpenAI
from crawler.tools.classification import classification_system_prompt, ClassificationResponse
from crawler.tools.embedding import EmbeddingMetadata


base_path = os.path.join(os.path.dirname(__file__), "raw")
source_dir = os.path.join(base_path, "ffmannersdorf")

# Initialize OpenAI client
client = OpenAI()

json_files = [f for f in os.listdir(source_dir) if f.endswith('.json')]

for index, json_file in enumerate(json_files):
    json_path = os.path.join(source_dir, json_file)

    # Read the JSON file to get the MarkdownMetadata
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            metadata_dict = json.load(f)
            metadata = EmbeddingMetadata.model_validate(metadata_dict)
    except Exception as e:
        print(f"Error reading metadata from {json_file}: {str(e)}")
        continue

    # Get the markdown file path
    markdown_file = metadata.markdown_file
    markdown_path = os.path.join(source_dir, markdown_file)

    # Check if the markdown file exists
    if not os.path.exists(markdown_path):
        print(f"Skipping {json_file} because markdown file {markdown_file} does not exist")
        continue

    # Read the markdown file
    try:
        with open(markdown_path, 'r', encoding='utf-8') as f:
            markdown_content = f.read()
    except Exception as e:
        print(f"Error reading markdown file {markdown_file}: {str(e)}")
        continue

    # Call OpenAI API to classify the document
    try:
        response = client.beta.chat.completions.parse(
            model="gpt-4.1-mini",  # Using GPT-4o for better classification
            messages=[
                {"role": "system", "content": classification_system_prompt},
                {"role": "user", "content": markdown_content}
            ],
            temperature=0.0,  # Use deterministic output
            response_format=ClassificationResponse,
        )

        classification = response.choices[0].message.parsed

        # Create a ClassificationMetadata object
        metadata.people_knowledge=classification.people_knowledge
        metadata.organisation_knowledge=classification.organisation_knowledge

        # Save the ClassificationMetadata to a JSON file in the target directory
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(metadata.model_dump(), f, indent=2)

        print(f"Classified {markdown_file} {index+1}/{len(json_files)}")


    except Exception as e:
        print(f"Error classifying {markdown_file}: {str(e)}")
        continue