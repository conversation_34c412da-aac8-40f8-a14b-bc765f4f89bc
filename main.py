from flask import Flask, request, jsonify, abort, render_template, Response, stream_with_context
from chat.chat import start_chat
import uuid
import json
import math
import time

app = Flask(__name__)

@app.route("/")
def hello_world():
    thread_id = str(uuid.uuid4())
    return render_template('chat.html', thread_id=thread_id)

def generate_auth_code() -> str:
    current_time = time.gmtime()
    hour = current_time.tm_hour
    minute = current_time.tm_min

    # something like 0.3048106211022167
    random_positive_number = abs(math.sin(71+hour-minute))
    return str(random_positive_number)[2:5]

@app.post("/api/v1/chat")
def chat():
    query = request.form['query']
    thread_id = request.form['thread_id']
    
    # this will prevent other from using this as a free api to chat
    if(request.form['auth_code'] != generate_auth_code()):
        time.sleep(10)
        abort(401)

    if(query is None or thread_id is None):
        abort(401)

    def generate():
        for chunk in start_chat(query, thread_id):
            yield f"data: {json.dumps({'message': chunk})}\n\n"

    return Response(stream_with_context(generate()), mimetype='text/event-stream')