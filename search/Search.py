from typing import Optional, TypedDict
from pymilvus import MilvusClient
import os
from mistralai import Mistral

# Initialize Mistral
mistral_client = Mistral(
    api_key=os.environ.get("MISTRAL_API_KEY"),
)

def get_embedding(text: str) -> list[float]:
    text = text.replace("\n", " ")

    response = mistral_client.embeddings.create(
        model="mistral-embed",
        inputs=[text]
    )
    return response.data[0].embedding

milvus_client = MilvusClient("data/milvus.db")


class ResultItem(TypedDict):
    sourcePath: str
    sourceName: str
    sourceTitle: str
    sourceDate: str
    text: str

def all_search(
    query: list[str],
    filter: Optional[str],
    limit: int = 5) -> list[ResultItem]:

    all_results = []

    # query the milvus database
    query_vector = get_embedding(query)
    res = milvus_client.search(
        collection_name="searchable_collection",
        data=[query_vector],
        limit=limit,
        filter=filter,
        output_fields=["sourcePath", "sourceName", "title", "sourceDate"]
    )
    all_results.extend(res[0])

    # remove duplicates from all_results
    seen = set()
    unique_res = []
    for obj in all_results:
        value = obj['entity']['sourcePath']
        if value not in seen:
            seen.add(value)
            unique_res.append(obj)
    
    
    # get the full parent for each chunk 
    files: list[ResultItem] = [item["entity"] for item in res[0]]
    for file in files:
        with open(file['sourcePath'], "r") as sourceFile:
            file['text'] = sourceFile.read()
    
    # if sort is not None:
        # sort files by file['sourceDate']
        # files.sort(key=lambda x: x['sourceDate'], reverse=True if sort == "desc" else False)
        # take the first n elemens in files where n is the limit
        # files = files[:limit]
    
    return files