* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON>l, sans-serif;
}

body {
    background-color: #f7f7f8;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.chat-container {
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
}

.chat-messages {
    flex-grow: 1;
    overflow-y: auto;
    padding: 20px;
    margin-bottom: 20px;
    position: relative;
}

.placeholder {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #6c757d;
}

.placeholder h1 {
    font-size: 2.5rem;
    font-weight: 600;
}

.message {
    margin-bottom: 20px;
    padding: 12px 16px;
    border-radius: 8px;
    max-width: 80%;
}

.user-message {
    background-color: #007AFF;
    color: white;
    margin-left: auto;
}

.bot-message {
    background-color: #E9ECEF;
    color: #212529;
}

.bot-message p {
    margin-bottom: 0.5em;
}

.bot-message p:last-child {
    margin-bottom: 0;
}

.bot-message code {
    background-color: rgba(0, 0, 0, 0.1);
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: monospace;
}

.bot-message pre {
    background-color: rgba(0, 0, 0, 0.1);
    padding: 1em;
    border-radius: 4px;
    overflow-x: auto;
    margin: 0.5em 0;
}

.bot-message pre code {
    background-color: transparent;
    padding: 0;
}

.bot-message ul, .bot-message ol {
    margin: 0.5em 0;
    padding-left: 1.5em;
}

.bot-message li {
    margin: 0.25em 0;
}

.bot-message a {
    color: #0056b3;
    text-decoration: none;
}

.bot-message a:hover {
    text-decoration: underline;
}

.bot-message blockquote {
    border-left: 3px solid rgba(0, 0, 0, 0.2);
    margin: 0.5em 0;
    padding-left: 1em;
    color: rgba(0, 0, 0, 0.7);
}

.loading-message {
    background-color: #E9ECEF;
    color: #212529;
    width: 60px;
    height: 20px;
    position: relative;
    overflow: hidden;
}

.loading-message::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.input-container {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

form {
    display: flex;
    gap: 10px;
}

input[type="text"] {
    flex-grow: 1;
    padding: 12px;
    border: 1px solid #E9ECEF;
    border-radius: 6px;
    font-size: 16px;
    outline: none;
}

input[type="text"]:focus {
    border-color: #007AFF;
}

button {
    padding: 12px 24px;
    background-color: #007AFF;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.2s;
}

button:hover {
    background-color: #0056b3;
}

button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.input-wrapper {
    position: relative;
    width: 100%;
    /* flex: 1; */
}

.input-wrapper input {
    width: 100%;
    padding-right: 5rem;
}

.char-counter {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-size: 0.9em;
} 