// Configure marked options
marked.setOptions({
    breaks: true,
    gfm: true,
    sanitize: true
});

function addMessage(content, isUser) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isUser ? 'user-message' : 'bot-message'}`;
    
    if (isUser) {
        messageDiv.textContent = content;
    } else {
        messageDiv.innerHTML = marked.parse(content);
    }
    
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    // Hide placeholder when first message is added
    const placeholder = document.getElementById('chat-placeholder');
    if (placeholder) {
        placeholder.style.display = 'none';
    }
    
    return messageDiv;
}

function addLoadingMessage() {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message loading-message';
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
    return messageDiv;
}

function generateAuthCode() {
    const now = new Date();
    const hour = now.getUTCHours();
    const minute = now.getUTCMinutes();

    // Replicating the Python logic: abs(math.sin(71 + hour - minute))
    const randomPositiveNumber = Math.abs(Math.sin(71 + hour - minute));
    return String(randomPositiveNumber).slice(2, 5);
}

async function handleSubmit(event) {
    event.preventDefault();
    const message = messageInput.value.trim();
    if (!message) return;

    // Disable input and button while processing
    messageInput.disabled = true;
    submitButton.disabled = true;

    // Add user message to chat
    addMessage(message, true);
    messageInput.value = '';

    // Add loading message
    const loadingMessage = addLoadingMessage();

    try {
        const formData = new FormData();
        formData.append('query', message);
        formData.append('thread_id', threadId);
        formData.append('auth_code', generateAuthCode()); // Add auth_code

        const response = await fetch('/api/v1/chat', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        // Remove loading message
        loadingMessage.remove();

        // Create a new message div for the streaming response
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message bot-message';
        chatMessages.appendChild(messageDiv);

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let accumulatedContent = '';

        while (true) {
            const { value, done } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');

            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    try {
                        const data = JSON.parse(line.slice(6));
                        accumulatedContent += data.message;
                        messageDiv.innerHTML = marked.parse(accumulatedContent);
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                    } catch (e) {
                        console.error('Error parsing SSE data:', e);
                    }
                }
            }
        }
    } catch (error) {
        console.error('Error:', error);
        loadingMessage.remove();
        addMessage('Sorry, there was an error processing your request.', false);
    } finally {
        // Re-enable input and button
        messageInput.disabled = false;
        submitButton.disabled = false;
        messageInput.focus();
    }
}