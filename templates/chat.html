<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>zemaGPT - Der Assistent für Zelking-Matzleinsdorf</title>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/chat.css') }}">
</head>
<body>
    <div class="chat-container">
        <div class="chat-messages" id="chat-messages">
            <div class="placeholder" id="chat-placeholder">
                <h1>zemaGPT</h1>
                <small>Der Assistent für Zelking-Matzleinsdorf</small>
            </div>
        </div>
        <div class="input-container">
            <form id="chat-form" onsubmit="handleSubmit(event)">
                <div class="input-wrapper">
                    <input type="text" id="message-input" placeholder="Deine Nachricht..." required maxlength="500" autofocus>
                    <span class="char-counter"><span id="char-counter">0</span>/500</span>
                </div>
                <button type="submit">Senden</button>
            </form>
        </div>
    </div>

    <script>
        // Global variables needed by chat.js
        const threadId = '{{ thread_id }}';
        const chatMessages = document.getElementById('chat-messages');
        const chatForm = document.getElementById('chat-form');
        const messageInput = document.getElementById('message-input');
        const submitButton = chatForm.querySelector('button');
        const charCounter = document.getElementById('char-counter');

        chatForm.addEventListener('submit', function(){
            charCounter.textContent = 0;
        })

        // Update character counter
        messageInput.addEventListener('input', function() {
            const remaining = this.value.length;
            charCounter.textContent = remaining;
        });
    </script>
    <script src="{{ url_for('static', filename='js/chat.js') }}"></script>
</body>
</html> 