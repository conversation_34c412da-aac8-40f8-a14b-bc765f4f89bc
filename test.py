from data.service import getAvailableOrganisations, getAvailablePeople, getKnowledge, semanticSearch


def getPersonInformation(namesAsFirstnameLastname: list[str]) -> str:

    knowledge: list[str] = []

    for nameAsFirstnameLastname in namesAsFirstnameLastname:
        person_knowledge = getKnowledge(nameAsFirstnameLastname)

        if person_knowledge is None or len(person_knowledge) < 1:
            knowledge.extend(f"Wir haben leider keine Informationen über {nameAsFirstnameLastname}.")
            continue
        text = '\n'.join(person_knowledge)

        print(f"person knowledge: {text}")

        knowledge.append(f"Hier sind Informationen zu {nameAsFirstnameLastname}:\n{text}")

    return {'\n\n'.join(knowledge)}

print(getPersonInformation(['<PERSON>', '<PERSON>']))
