#!/usr/bin/env python3
"""
Simple test script for the research subgraph.
This script tests the research graph with a sample query.
"""

import os
import sys
from langchain_core.messages import HumanMessage

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from chat.graphs.research import research_graph

def test_research_graph():
    """Test the research graph with a sample query."""
    
    # Test query about Zelking-Matzleinsdorf
    test_prompt = "Wer ist der Bürgermeister von <PERSON>elking-Matzleinsdorf?"
    
    # Initial state
    initial_state = {
        "prompt": test_prompt,
        "messages": [HumanMessage(content=test_prompt)]
    }
    
    print(f"Testing research graph with query: {test_prompt}")
    print("=" * 50)
    
    try:
        # Run the research graph
        result = research_graph.invoke(initial_state)
        
        print("Research completed successfully!")
        print(f"Final result: {result['messages'][-1].content}")
        print(f"Total iterations: {result.get('research_iterations', 'Unknown')}")
        print(f"Information collected: {len(result.get('collected_information', []))} items")
        
        return True
        
    except Exception as e:
        print(f"Error during research: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_research_graph()
    sys.exit(0 if success else 1)
