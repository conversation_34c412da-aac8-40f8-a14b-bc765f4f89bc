import os
import json
import shutil
import unittest
from unittest.mock import patch
from crawler.curated.people_knowledge import (
    generate_people_knowledge,
    identify_people_and_non_people,
    process_duplicates_and_non_people,
    PersonDuplicate
)
from crawler.tools.classification_model import OrganisationKnowledge

class TestPeopleKnowledge(unittest.TestCase):

    def setUp(self):
        # Create a temporary test directory
        self.test_data_repo_path = "test_data_repo"
        self.test_raw_path = os.path.join(self.test_data_repo_path, "raw")
        self.test_people_knowledge_path = os.path.join(self.test_data_repo_path, "people_knowledge")

        # Create test directories
        os.makedirs(self.test_raw_path, exist_ok=True)

        # Create test subfolders
        self.test_subfolder1 = os.path.join(self.test_raw_path, "subfolder1")
        self.test_subfolder2 = os.path.join(self.test_raw_path, "subfolder2")
        os.makedirs(self.test_subfolder1, exist_ok=True)
        os.makedirs(self.test_subfolder2, exist_ok=True)

        # Create test JSON files with people_knowledge
        self.create_test_json_files()

    def tearDown(self):
        # Clean up test directories
        if os.path.exists(self.test_data_repo_path):
            shutil.rmtree(self.test_data_repo_path)

    def create_test_json_files(self):
        # Create test JSON file 1
        test_json1 = {
            "step": "classification",
            "people_knowledge": [
                {
                    "firstname": "John",
                    "lastname": "Doe",
                    "title": "Dr.",
                    "knowledge_sentences_about_that_person": [
                        "John Doe is a doctor.",
                        "He works at the hospital."
                    ]
                },
                {
                    "firstname": "Jane",
                    "lastname": "Smith",
                    "title": None,
                    "knowledge_sentences_about_that_person": [
                        "Jane Smith is a teacher."
                    ]
                }
            ]
        }

        # Create test JSON file 2
        test_json2 = {
            "step": "classification",
            "people_knowledge": [
                {
                    "firstname": "John",
                    "lastname": "Doe",
                    "title": "Dr.",
                    "knowledge_sentences_about_that_person": [
                        "John Doe is a doctor.",
                        "He has been working for 10 years."
                    ]
                },
                {
                    "firstname": "Bob",
                    "lastname": "Johnson",
                    "title": "Prof.",
                    "knowledge_sentences_about_that_person": [
                        "Bob Johnson is a professor."
                    ]
                }
            ]
        }

        # Write test JSON files
        with open(os.path.join(self.test_subfolder1, "test1.json"), "w") as f:
            json.dump(test_json1, f)

        with open(os.path.join(self.test_subfolder2, "test2.json"), "w") as f:
            json.dump(test_json2, f)

    @patch('crawler.curated.people_knowledge.data_repo_path', new_callable=lambda: "test_data_repo")
    @patch('crawler.curated.people_knowledge.identify_duplicates_and_non_people')
    def test_generate_people_knowledge(self, mock_identify, _):
        # Mock the identify_duplicates_and_non_people function to return an empty list
        mock_identify.return_value = []

        # Call the function
        generate_people_knowledge()

        # Check if the output file was created
        output_file = os.path.join(self.test_people_knowledge_path, "data.json")
        self.assertTrue(os.path.exists(output_file))

        # Check the content of the output file
        with open(output_file, "r") as f:
            data = json.load(f)

        # Should have 3 unique people
        self.assertEqual(len(data), 3)

        # Check if John Doe has 3 unique knowledge sentences
        john_key = "john_doe"
        self.assertIn(john_key, data)
        self.assertEqual(len(data[john_key]["knowledge_sentences_about_that_person"]), 3)

    def test_identify_duplicates_and_non_people(self):
        # This would normally call OpenAI, so we'll just test the empty case
        result = identify_people_and_non_people([])
        self.assertEqual(result, [])

    def test_process_duplicates_and_non_people(self):
        # Create test data
        combined_people = {
            "john_doe": OrganisationKnowledge(
                firstname="John",
                lastname="Doe",
                title="Dr.",
                knowledge_sentences_about_that_person=["John Doe is a doctor."]
            ),
            "jane_smith": OrganisationKnowledge(
                firstname="Jane",
                lastname="Smith",
                title=None,
                knowledge_sentences_about_that_person=["Jane Smith is a teacher."]
            ),
            "not_a_person": OrganisationKnowledge(
                firstname="Not",
                lastname="Person",
                title=None,
                knowledge_sentences_about_that_person=["This is not a real person."]
            )
        }

        people_names = ["Dr. John Doe", "Jane Smith", "Not Person"]

        duplicates_and_non_people = [
            PersonDuplicate(
                original_names=["Not Person"],
                is_person=False,
                merged_name=None
            )
        ]

        # Process the duplicates and non-people
        result = process_duplicates_and_non_people(
            combined_people,
            duplicates_and_non_people,
            people_names
        )

        # Check the result
        self.assertEqual(len(result), 2)  # Should have removed "Not Person"
        self.assertIn("john_doe", result)
        self.assertIn("jane_smith", result)
        self.assertNotIn("not_a_person", result)

if __name__ == "__main__":
    unittest.main()
